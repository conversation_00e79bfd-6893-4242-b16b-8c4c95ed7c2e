# LibreOffice Draw 快速重绘指导

## 🎯 目标：快速重新绘制图1，避免连接线问题

### 📋 准备工作
1. 新建LibreOffice Draw文档
2. 参考当前SVG图片的布局
3. 使用基本工具重新绘制

## 🚀 快速绘制步骤

### Step 1: 创建标题
1. **文本工具** → 输入"图1 系统整体架构图"
2. **格式**：微软雅黑，16pt，加粗，居中

### Step 2: 快速创建模块模板
1. **绘制第一个模块**：
   - 矩形工具 → 绘制矩形（约3cm×1cm）
   - 双击输入"用户界面模块"
   - 设置颜色：浅蓝色
   - 设置边框：1pt黑色

2. **复制模板**：
   - 选中模块 → Ctrl+C复制
   - Ctrl+V粘贴 → 修改文字和颜色
   - 重复此过程创建所有模块

### Step 3: 模块列表和颜色
**按层级创建（从上到下）：**

**第1层：**
- 用户界面模块 [浅蓝色 #e1f5fe]

**第2层：**
- 主控制器 [浅紫色 #f3e5f5]

**第3层（并排）：**
- DNB1101BB芯片模块 [浅橙色 #fff3e0]
- 外部电流源模块 [浅橙色 #fff3e0]
- 通信接口模块 [浅橙色 #fff3e0]

**第4层（对应上层）：**
- 阻抗测量电路 [白色]
- 电流控制电路 [白色]
- Modbus RTU通信 [白色]

**第5层：**
- 信号调理电路 [白色]

**第6层：**
- ADC采样 [白色]

**第7层：**
- 数据处理模块 [浅绿色 #e8f5e8]

**第8层（并排）：**
- 多维参数提取算法 [白色]
- 九档智能分组决策 [白色]
- 增益自适应调节 [白色]

**第9层：**
- 测试结果输出 [浅粉色 #fce4ec]

**第10层（并排）：**
- 数据存储 [白色]
- 结果显示 [白色]
- 上位机通信 [白色]

### Step 4: 智能连接线绘制

**使用连接器工具的技巧：**

1. **直接连接**（适用于垂直连接）：
   - 用户界面 → 主控制器
   - ADC采样 → 数据处理模块
   - 测试结果输出 → 结果显示

2. **L型连接**（避免穿过其他模块）：
   - 主控制器 → 三个硬件模块
   - 数据处理 → 三个算法模块
   - 算法模块 → 测试结果输出

3. **特殊连接**（Modbus通信）：
   - 从"Modbus RTU通信"向右拉出
   - 再向下到页面底部
   - 最后向左连接到"上位机通信"

### Step 5: 连接线优化技巧

**避免连接线交叉的方法：**

1. **分层连接**：
   ```
   主控制器
   ├── 向左下 → DNB1101BB芯片模块
   ├── 向正下 → 外部电流源模块
   └── 向右下 → 通信接口模块
   ```

2. **汇聚连接**：
   ```
   阻抗测量电路 ┐
                 ├→ 信号调理电路
   电流控制电路 ┘
   ```

3. **分散连接**：
   ```
                 ┌→ 多维参数提取算法
   数据处理模块 ├→ 九档智能分组决策
                 └→ 增益自适应调节
   ```

### Step 6: 设置连接线样式

**统一的连接线格式：**
1. **选中连接线** → 右键 → 线条
2. **宽度**：1.5pt
3. **颜色**：深灰色或黑色
4. **结束箭头**：实心箭头
5. **开始箭头**：无

### Step 7: 最终调整

**对齐和美化：**
1. **选择多个对象** → 格式 → 对齐 → 居中对齐
2. **调整间距**：保持各层间距一致
3. **检查连接**：确保所有连接线都有箭头
4. **文字检查**：确保所有文字清晰可读

## ⚡ 快速操作技巧

### 批量操作
1. **复制相似模块**：创建一个标准模块后，复制粘贴修改
2. **批量设置颜色**：选中多个同类模块，一次性设置颜色
3. **对齐工具**：使用对齐工具保持整齐

### 连接线技巧
1. **网格对齐**：视图 → 网格 → 显示网格
2. **捕捉功能**：启用对象捕捉，连接线自动对齐到模块边缘
3. **连接点**：在模块边缘会显示连接点，拖拽到连接点

### 颜色设置快捷方式
**RGB颜色值：**
- 浅蓝色：225,245,254
- 浅紫色：243,229,245
- 浅橙色：255,243,224
- 浅绿色：232,245,232
- 浅粉色：252,228,236

## 🎯 预计时间
- **熟练操作**：15-20分钟
- **首次绘制**：30-40分钟
- **包含调整**：45-60分钟

## 💡 成功关键
1. **先创建模板**，再批量复制
2. **分层绘制**，从上到下
3. **最后绘制连接线**，避免移动时断开
4. **使用网格和对齐工具**保持整齐

这种方法虽然需要手动绘制，但可以完全控制每个元素，避免SVG转换的问题。
