REM LibreOffice Draw 自动绘图宏
REM 用于快速创建EIS专利图片的基本形状和布局

Sub 创建图1_系统整体架构图()
    Dim oDoc As Object
    Dim oPage As Object
    Dim oShape As Object
    
    ' 获取当前文档
    oDoc = ThisComponent
    oPage = oDoc.getDrawPages().getByIndex(0)
    
    ' 创建标题
    oShape = oDoc.createInstance("com.sun.star.drawing.TextShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 5000, 1000))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 8000, 800))
    oShape.getString() = "图1 系统整体架构图"
    oPage.add(oShape)
    
    ' 创建用户界面模块
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 2500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 3000, 1000))
    oShape.getString() = "用户界面模块"
    oShape.FillColor = RGB(225, 245, 254) ' 浅蓝色
    oPage.add(oShape)
    
    ' 创建主控制器
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 4500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 3000, 1000))
    oShape.getString() = "主控制器"
    oShape.FillColor = RGB(243, 229, 245) ' 浅紫色
    oPage.add(oShape)
    
    ' 创建DNB1101BB芯片模块
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 3000, 6500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 3000, 1000))
    oShape.getString() = "DNB1101BB芯片模块"
    oShape.FillColor = RGB(255, 243, 224) ' 浅橙色
    oPage.add(oShape)
    
    ' 创建外部电流源模块
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 6500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 3000, 1000))
    oShape.getString() = "外部电流源模块"
    oShape.FillColor = RGB(255, 243, 224) ' 浅橙色
    oPage.add(oShape)
    
    ' 创建通信接口模块
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 11000, 6500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 3000, 1000))
    oShape.getString() = "通信接口模块"
    oShape.FillColor = RGB(255, 243, 224) ' 浅橙色
    oPage.add(oShape)
    
    MsgBox "图1基本框架已创建，请手动添加连接线和其他模块"
End Sub

Sub 创建基本流程图形状()
    Dim oDoc As Object
    Dim oPage As Object
    Dim oShape As Object
    
    oDoc = ThisComponent
    oPage = oDoc.getDrawPages().getByIndex(0)
    
    ' 创建开始椭圆
    oShape = oDoc.createInstance("com.sun.star.drawing.EllipseShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 2000))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 2000, 800))
    oShape.getString() = "开始"
    oShape.FillColor = RGB(200, 230, 201) ' 浅绿色
    oPage.add(oShape)
    
    ' 创建处理矩形
    oShape = oDoc.createInstance("com.sun.star.drawing.RectangleShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 3500))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 2000, 800))
    oShape.getString() = "处理步骤"
    oShape.FillColor = RGB(255, 255, 255) ' 白色
    oPage.add(oShape)
    
    ' 创建判断菱形（使用多边形近似）
    oShape = oDoc.createInstance("com.sun.star.drawing.PolyPolygonShape")
    ' 菱形坐标点
    Dim aPoints(3) As Object
    aPoints(0) = createUnoStruct("com.sun.star.awt.Point", 8000, 5000) ' 上
    aPoints(1) = createUnoStruct("com.sun.star.awt.Point", 9000, 5500) ' 右
    aPoints(2) = createUnoStruct("com.sun.star.awt.Point", 8000, 6000) ' 下
    aPoints(3) = createUnoStruct("com.sun.star.awt.Point", 7000, 5500) ' 左
    
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 7000, 5000))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 2000, 1000))
    oShape.getString() = "判断条件?"
    oShape.FillColor = RGB(255, 249, 196) ' 浅黄色
    oPage.add(oShape)
    
    MsgBox "基本流程图形状已创建"
End Sub

Sub 设置图形样式()
    ' 设置默认字体和样式的宏
    Dim oDoc As Object
    oDoc = ThisComponent
    
    ' 设置默认字体
    Dim oStyle As Object
    oStyle = oDoc.getStyleFamilies().getByName("graphics").getByName("standard")
    oStyle.CharFontName = "微软雅黑"
    oStyle.CharHeight = 12
    oStyle.CharWeight = 100 ' 正常粗细
    
    MsgBox "图形样式已设置为微软雅黑12pt"
End Sub

Sub 创建连接线()
    ' 创建连接线的辅助宏
    Dim oDoc As Object
    Dim oPage As Object
    Dim oShape As Object
    
    oDoc = ThisComponent
    oPage = oDoc.getDrawPages().getByIndex(0)
    
    ' 创建箭头连接线
    oShape = oDoc.createInstance("com.sun.star.drawing.ConnectorShape")
    oShape.setPosition(createUnoStruct("com.sun.star.awt.Point", 8000, 2800))
    oShape.setSize(createUnoStruct("com.sun.star.awt.Size", 0, 700))
    
    ' 设置箭头样式
    oShape.LineEndName = "Arrow"
    oShape.LineWidth = 50 ' 线条粗细
    
    oPage.add(oShape)
    
    MsgBox "连接线已创建，请手动调整连接点"
End Sub

REM 使用说明：
REM 1. 在LibreOffice Draw中按Alt+F11打开宏编辑器
REM 2. 将此代码粘贴到新模块中
REM 3. 运行对应的宏函数来快速创建图形
REM 4. 手动调整位置和添加连接线
