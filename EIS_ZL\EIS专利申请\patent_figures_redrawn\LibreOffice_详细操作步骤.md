# LibreOffice Draw 详细操作步骤

## 🎯 目标：绘制图1_系统整体架构图

### 📋 准备工作
1. 确保LibreOffice Draw已打开
2. 创建新文档：文件 → 新建 → 绘图
3. 设置页面：格式 → 页面 → A4横向

### 🛠️ 工具栏位置
- **绘图工具栏**：通常在左侧或底部
- **矩形工具**：绘图工具栏中的方形图标
- **文本工具**：绘图工具栏中的"A"图标
- **连接器工具**：绘图工具栏中的箭头图标

## 📝 详细绘制步骤

### Step 1: 创建标题
1. 点击**文本工具**（A图标）
2. 在页面上方点击并拖拽创建文本框
3. 输入："图1 系统整体架构图"
4. 选中文本，设置格式：
   - 字体：微软雅黑
   - 大小：16pt
   - 加粗：Ctrl+B
   - 居中：Ctrl+E

### Step 2: 创建第一层 - 用户界面模块
1. 点击**矩形工具**
2. 在页面上方中央绘制矩形（约3cm×1cm）
3. 双击矩形，输入："用户界面模块"
4. 设置颜色：
   - 右键矩形 → 区域
   - 颜色：浅蓝色（RGB: 225,245,254）
   - 确定

### Step 3: 创建第二层 - 主控制器
1. 在用户界面模块下方绘制矩形
2. 输入文字："主控制器"
3. 设置颜色：浅紫色（RGB: 243,229,245）

### Step 4: 创建第三层 - 三个并排模块
**从左到右依次创建：**

1. **DNB1101BB芯片模块**
   - 位置：主控制器左下方
   - 文字："DNB1101BB芯片模块"
   - 颜色：浅橙色（RGB: 255,243,224）

2. **外部电流源模块**
   - 位置：主控制器正下方
   - 文字："外部电流源模块"
   - 颜色：浅橙色

3. **通信接口模块**
   - 位置：主控制器右下方
   - 文字："通信接口模块"
   - 颜色：浅橙色

### Step 5: 创建第四层 - 对应电路模块
**在第三层模块下方创建：**

1. **阻抗测量电路**（DNB1101BB下方）
2. **电流控制电路**（外部电流源下方）
3. **Modbus RTU通信**（通信接口下方）
   - 颜色：白色（默认）

### Step 6: 创建汇聚层
1. **信号调理电路**
   - 位置：阻抗测量和电流控制电路下方中央
   - 宽度：跨越两个上层模块

2. **ADC采样**
   - 位置：信号调理电路下方

3. **数据处理模块**
   - 位置：ADC采样下方
   - 颜色：浅绿色（RGB: 232,245,232）

### Step 7: 创建算法层
**在数据处理模块下方创建三个并排模块：**

1. **多维参数提取算法**（左）
2. **九档智能分组决策**（中）
3. **增益自适应调节**（右）

### Step 8: 创建输出层
1. **测试结果输出**
   - 位置：三个算法模块下方中央
   - 颜色：浅粉色（RGB: 252,228,236）

2. **最终输出模块**（并排三个）：
   - **数据存储**（左）
   - **结果显示**（中）
   - **上位机通信**（右）

### Step 9: 添加连接线
1. 点击**连接器工具**（箭头图标）
2. 从起始模块的边缘点击并拖拽到目标模块
3. 设置箭头样式：
   - 右键连接线 → 线条
   - 结束箭头：选择实心箭头
   - 线条宽度：1-2pt

**主要连接关系：**
- 用户界面 → 主控制器
- 主控制器 → 三个硬件模块
- 硬件模块 → 对应电路
- 电路 → 信号调理 → ADC → 数据处理
- 数据处理 → 三个算法
- 算法 → 测试结果输出
- 测试结果 → 三个输出模块

### Step 10: 最终调整
1. **对齐模块**：
   - 选中多个对象：Ctrl+点击
   - 格式 → 对齐 → 居中对齐

2. **调整间距**：
   - 保持各层之间距离一致
   - 同层模块间距相等

3. **检查文字**：
   - 确保所有文字清晰可读
   - 字体大小一致（建议10-12pt）

### Step 11: 保存文件
1. **保存为ODG格式**：
   - 文件 → 另存为
   - 文件类型：OpenDocument绘图(.odg)
   - 文件名：图1_系统整体架构图.odg

2. **导出为其他格式**：
   - 文件 → 导出为PDF
   - 文件 → 导出 → 导出为PNG

## 🎨 颜色参考（RGB值）
- 浅蓝色：225,245,254
- 浅紫色：243,229,245
- 浅橙色：255,243,224
- 浅绿色：232,245,232
- 浅粉色：252,228,236
- 浅黄色：255,249,196

## ⚡ 快捷键
- **复制**：Ctrl+C
- **粘贴**：Ctrl+V
- **撤销**：Ctrl+Z
- **全选**：Ctrl+A
- **保存**：Ctrl+S
- **缩放**：Ctrl+鼠标滚轮

## 💡 小技巧
1. **复制相似模块**：创建一个模块后，复制粘贴可快速创建相似模块
2. **网格对齐**：视图 → 网格 → 显示网格，帮助对齐
3. **群组对象**：选中多个对象后右键 → 群组，便于整体移动
4. **图层管理**：使用图层分别管理文字、形状、连接线

## ❗ 注意事项
1. 保持模块大小基本一致
2. 连接线不要交叉
3. 文字要居中对齐
4. 颜色搭配要协调
5. 整体布局要平衡
