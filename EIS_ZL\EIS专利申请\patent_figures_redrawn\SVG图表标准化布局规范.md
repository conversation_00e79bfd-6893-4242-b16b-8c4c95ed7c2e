# SVG图表标准化布局规范

## 🎯 目标
建立统一的SVG图表布局标准，彻底解决文字超出框框、布局混乱等问题，确保所有专利附图的专业性和一致性。

## 📏 基本布局原则

### 1. 安全边距规则
```
框框内容安全区域 = 框框尺寸 - 2 × 安全边距
安全边距 ≥ 20px（所有方向）
```

### 2. 文字布局标准
```css
.param-text {
  font-family: "Microsoft YaHei", Arial;
  font-size: 8px;
  text-anchor: start;
  fill: #333;
}
```

### 3. 框框尺寸计算
```
框框宽度 = 内容最大宽度 + 40px（左右各20px边距）
框框高度 = 行数 × 行高 + 40px（上下各20px边距）
行高 = 字体大小 + 行间距（推荐15px）
```

### 4. 层次关系规则 ⭐
```
绘制顺序（从底层到顶层）：
第一层：箭头线和连接线（最底层，不压框框）
第二层：信号线和基准线
第三层：框框和矩形（可以压线）
第四层：文字和标签（最顶层）

重要原则：
- 线不要压框框
- 框框可以压线
- 文字始终在最顶层
```

## 📐 标准化模板

### 参数说明框模板
```xml
<!-- 参数说明框 - 标准模板 -->
<rect x="80" y="Y坐标" width="计算宽度" height="计算高度" 
      fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
<text x="中心X坐标" y="Y坐标+25" class="section-title">标题</text>

<!-- 左列参数 -->
<text x="100" y="Y坐标+50" class="param-text">分类标题:</text>
<text x="100" y="Y坐标+65" class="param-text">• 参数1: 值1</text>
<text x="100" y="Y坐标+80" class="param-text">• 参数2: 值2</text>

<!-- 中列参数 -->
<text x="350" y="Y坐标+50" class="param-text">分类标题:</text>
<text x="350" y="Y坐标+65" class="param-text">• 参数1: 值1</text>
<text x="350" y="Y坐标+80" class="param-text">• 参数2: 值2</text>

<!-- 右列参数 -->
<text x="600" y="Y坐标+50" class="param-text">分类标题:</text>
<text x="600" y="Y坐标+65" class="param-text">• 参数1: 值1</text>
<text x="600" y="Y坐标+80" class="param-text">• 参数2: 值2</text>
```

### 列宽度分配标准
```
总宽度1040px的分配：
- 左列：100-330px（宽度230px）
- 中列：350-580px（宽度230px）  
- 右列：600-830px（宽度230px）
- 最右列：850-1080px（宽度230px）
```

## 🔧 具体实施规范

### 字体大小标准
```
标题文字：18px（.title）
设备标签：14px（.device-label）
数据标签：9px（.data-label）
参数文字：8px（.param-text）
字段标签：8px（.field-label）
```

### 颜色标准
```
背景色：#f5f5f5
边框色：#666
文字色：#333
标题色：#000
辅助色：#666
```

### 间距标准
```
行间距：15px
段落间距：20px
列间距：20px
框框边距：20px
```

## ✅ 质量检查清单

### 布局检查
- [ ] 所有文字都在框框安全区域内
- [ ] 左右边距≥20px
- [ ] 上下边距≥20px
- [ ] 列间距≥20px
- [ ] 行间距≥15px

### 层次关系检查 ⭐
- [ ] 箭头线在最底层（不压框框）
- [ ] 信号线在第二层
- [ ] 框框在第三层（可以压线）
- [ ] 文字在最顶层
- [ ] 无元素层次冲突

### 文字检查
- [ ] 字体大小统一（8px参数文字）
- [ ] 文字对齐方式正确（text-anchor: start）
- [ ] 颜色符合标准
- [ ] 无文字重叠

### 框框检查
- [ ] 框框大小适合内容
- [ ] 边框样式统一
- [ ] 圆角半径统一（rx="5"）
- [ ] 填充色统一

## 🛠️ 修正流程

### 发现文字超出时的处理步骤：
1. **测量内容宽度**：计算最长行的文字宽度
2. **调整框框宽度**：新宽度 = 内容宽度 + 40px
3. **重新分配列宽**：确保各列间距≥20px
4. **验证边距**：确保左右边距≥20px
5. **测试显示效果**：在浏览器中验证

### 预防措施：
1. **设计阶段**：先规划内容，再确定框框大小
2. **内容控制**：限制每行文字长度
3. **分列显示**：长内容分多列显示
4. **缩写使用**：适当使用技术缩写

## 📋 常用尺寸参考

### 标准框框尺寸
```
小型参数框：800×100px
中型参数框：1000×150px
大型参数框：1200×200px
```

### 标准画布尺寸
```
简单图表：1000×600px
复杂图表：1200×800px
详细图表：1200×1000px
```

## 🎨 样式库

### CSS样式定义
```css
.title { 
  font-family: "Microsoft YaHei", Arial; 
  font-size: 18px; 
  font-weight: bold; 
  text-anchor: middle; 
}

.device-label { 
  font-family: "Microsoft YaHei", Arial; 
  font-size: 14px; 
  font-weight: bold; 
  text-anchor: start; 
}

.param-text { 
  font-family: "Microsoft YaHei", Arial; 
  font-size: 8px; 
  text-anchor: start; 
  fill: #333; 
}

.section-title { 
  font-family: "Microsoft YaHei", Arial; 
  font-size: 12px; 
  font-weight: bold; 
  text-anchor: middle; 
}
```

## 🔄 版本控制

### 文件命名规范
```
原始版本：图X_名称.svg
修正版本：图X_名称_修正版.svg
标准版本：图X_名称_标准版.svg
最终版本：图X_名称_最终版.svg
```

### 修改记录
每次修改都应记录：
- 修改时间
- 修改内容
- 修改原因
- 验证结果

## 📞 应用指南

### 新建图表时：
1. 先确定内容和布局
2. 按照标准模板创建
3. 严格遵循尺寸规范
4. 完成后进行质量检查

### 修改现有图表时：
1. 识别问题类型
2. 按照修正流程处理
3. 应用标准化规范
4. 验证修改效果

---

**注意：严格遵循此规范，可以彻底避免文字超出框框等布局问题的反复出现！**
