<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .module { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 12px; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .connection { stroke: #666; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">图1 系统整体架构图</text>
  
  <!-- 第一层：用户界面模块 -->
  <rect x="300" y="60" width="200" height="50" fill="#e1f5fe" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="90" class="module">用户界面模块</text>
  
  <!-- 第二层：主控制器 -->
  <rect x="300" y="150" width="200" height="50" fill="#f3e5f5" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="180" class="module">主控制器</text>
  
  <!-- 第三层：三个并排模块 -->
  <rect x="100" y="240" width="150" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="175" y="270" class="module">DNB1101BB芯片模块</text>
  
  <rect x="325" y="240" width="150" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="270" class="module">外部电流源模块</text>
  
  <rect x="550" y="240" width="150" height="50" fill="#fff3e0" stroke="#333" stroke-width="1" rx="5"/>
  <text x="625" y="270" class="module">通信接口模块</text>
  
  <!-- 第四层：对应的电路模块 -->
  <rect x="100" y="330" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="175" y="360" class="module">阻抗测量电路</text>
  
  <rect x="325" y="330" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="360" class="module">电流控制电路</text>
  
  <rect x="550" y="330" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="625" y="360" class="module">Modbus RTU通信</text>
  
  <!-- 第五层：信号调理电路 -->
  <rect x="200" y="420" width="200" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="300" y="450" class="module">信号调理电路</text>
  
  <!-- 第六层：ADC采样 -->
  <rect x="300" y="510" width="200" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="540" class="module">ADC采样</text>
  
  <!-- 第七层：数据处理模块 -->
  <rect x="300" y="600" width="200" height="50" fill="#e8f5e8" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="630" class="module">数据处理模块</text>
  
  <!-- 第八层：三个算法模块 -->
  <rect x="50" y="690" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="125" y="720" class="module">多维参数提取算法</text>
  
  <rect x="325" y="690" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="720" class="module">九档智能分组决策</text>
  
  <rect x="600" y="690" width="150" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="675" y="720" class="module">增益自适应调节</text>
  
  <!-- 第九层：测试结果输出 -->
  <rect x="300" y="780" width="200" height="50" fill="#fce4ec" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="810" class="module">测试结果输出</text>
  
  <!-- 第十层：三个输出模块 -->
  <rect x="100" y="870" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="160" y="900" class="module">数据存储</text>
  
  <rect x="340" y="870" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="400" y="900" class="module">结果显示</text>
  
  <rect x="580" y="870" width="120" height="50" fill="#ffffff" stroke="#333" stroke-width="1" rx="5"/>
  <text x="640" y="900" class="module">上位机通信</text>
  
  <!-- 连接线 -->
  <!-- 用户界面 → 主控制器 -->
  <line x1="400" y1="110" x2="400" y2="150" class="arrow"/>
  
  <!-- 主控制器 → 三个模块 -->
  <line x1="350" y1="200" x2="175" y2="240" class="arrow"/>
  <line x1="400" y1="200" x2="400" y2="240" class="arrow"/>
  <line x1="450" y1="200" x2="625" y2="240" class="arrow"/>
  
  <!-- 三个模块 → 对应电路 -->
  <line x1="175" y1="290" x2="175" y2="330" class="arrow"/>
  <line x1="400" y1="290" x2="400" y2="330" class="arrow"/>
  <line x1="625" y1="290" x2="625" y2="330" class="arrow"/>
  
  <!-- 电路 → 信号调理 -->
  <line x1="175" y1="380" x2="250" y2="420" class="arrow"/>
  <line x1="400" y1="380" x2="350" y2="420" class="arrow"/>
  
  <!-- 信号调理 → ADC -->
  <line x1="350" y1="470" x2="400" y2="510" class="arrow"/>
  
  <!-- ADC → 数据处理 -->
  <line x1="400" y1="560" x2="400" y2="600" class="arrow"/>
  
  <!-- 数据处理 → 三个算法 -->
  <line x1="350" y1="650" x2="125" y2="690" class="arrow"/>
  <line x1="400" y1="650" x2="400" y2="690" class="arrow"/>
  <line x1="450" y1="650" x2="675" y2="690" class="arrow"/>
  
  <!-- 三个算法 → 测试结果 -->
  <line x1="125" y1="740" x2="350" y2="780" class="arrow"/>
  <line x1="400" y1="740" x2="400" y2="780" class="arrow"/>
  <line x1="675" y1="740" x2="450" y2="780" class="arrow"/>
  
  <!-- 测试结果 → 三个输出 -->
  <line x1="350" y1="830" x2="160" y2="870" class="arrow"/>
  <line x1="400" y1="830" x2="400" y2="870" class="arrow"/>
  <line x1="450" y1="830" x2="640" y2="870" class="arrow"/>
  
  <!-- 通信模块的特殊连接 -->
  <line x1="625" y1="380" x2="640" y2="870" class="connection"/>
</svg>
