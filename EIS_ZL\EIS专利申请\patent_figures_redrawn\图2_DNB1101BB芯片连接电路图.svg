<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .chip-label { font-family: "Microsoft YaHei", <PERSON>l; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .pin-label { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .component-label { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; }
      .value-label { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: middle; fill: #666; }
      .connection { stroke: #333; stroke-width: 1.5; fill: none; }
      .power-line { stroke: #d32f2f; stroke-width: 2; fill: none; }
      .ground-line { stroke: #388e3c; stroke-width: 2; fill: none; }
      .signal-line { stroke: #1976d2; stroke-width: 1.5; fill: none; }
    </style>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">图2 DNB1101BB芯片连接电路图</text>
  
  <!-- 中央芯片区域 -->
  <rect x="400" y="200" width="200" height="300" fill="#f5f5f5" stroke="#333" stroke-width="2" rx="5"/>
  <text x="500" y="225" class="chip-label">DNB1101BB芯片</text>
  
  <!-- 芯片引脚 - 左侧 -->
  <!-- 电源引脚 -->
  <rect x="380" y="250" width="20" height="15" fill="#ffcdd2" stroke="#333"/>
  <text x="370" y="260" class="pin-label">VDD</text>
  <text x="320" y="260" class="pin-label">+3.3V</text>
  
  <rect x="380" y="280" width="20" height="15" fill="#c8e6c9" stroke="#333"/>
  <text x="370" y="290" class="pin-label">VSS</text>
  <text x="320" y="290" class="pin-label">GND</text>
  
  <!-- 参考电压引脚 -->
  <rect x="380" y="320" width="20" height="15" fill="#fff9c4" stroke="#333"/>
  <text x="370" y="330" class="pin-label">REFIN+</text>
  <text x="310" y="330" class="pin-label">+2.5V</text>
  
  <rect x="380" y="350" width="20" height="15" fill="#fff9c4" stroke="#333"/>
  <text x="370" y="360" class="pin-label">REFIN-</text>
  <text x="320" y="360" class="pin-label">GND</text>
  
  <!-- 信号输入引脚 -->
  <rect x="380" y="390" width="20" height="15" fill="#e1f5fe" stroke="#333"/>
  <text x="370" y="400" class="pin-label">AIN0</text>
  <text x="300" y="400" class="pin-label">电压信号+</text>
  
  <rect x="380" y="420" width="20" height="15" fill="#e1f5fe" stroke="#333"/>
  <text x="370" y="430" class="pin-label">AIN1</text>
  <text x="300" y="430" class="pin-label">电压信号-</text>
  
  <!-- 芯片引脚 - 右侧 -->
  <!-- SPI通信引脚 -->
  <rect x="600" y="250" width="20" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="630" y="260" class="pin-label">SCLK</text>
  <text x="680" y="260" class="pin-label">MCU_SCLK</text>
  
  <rect x="600" y="280" width="20" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="630" y="290" class="pin-label">MISO</text>
  <text x="680" y="290" class="pin-label">MCU_MISO</text>
  
  <rect x="600" y="310" width="20" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="630" y="320" class="pin-label">MOSI</text>
  <text x="680" y="320" class="pin-label">MCU_MOSI</text>
  
  <rect x="600" y="340" width="20" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="630" y="350" class="pin-label">CS</text>
  <text x="680" y="350" class="pin-label">MCU_CS</text>
  
  <!-- 左侧电源电路 -->
  <rect x="150" y="240" width="80" height="30" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="190" y="260" class="component-label">+3.3V电源</text>
  
  <rect x="150" y="280" width="80" height="30" fill="#e8f5e8" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="190" y="300" class="component-label">GND</text>
  
  <!-- 参考电压电路 -->
  <rect x="150" y="320" width="80" height="30" fill="#fffde7" stroke="#f57f17" stroke-width="1" rx="3"/>
  <text x="190" y="340" class="component-label">+2.5V基准</text>
  
  <!-- 信号输入电路 -->
  <rect x="120" y="380" width="100" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="170" y="400" class="component-label">电压信号输入</text>
  
  <rect x="120" y="420" width="100" height="30" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="170" y="440" class="component-label">电压信号输入</text>
  
  <!-- 右侧MCU接口 -->
  <rect x="750" y="240" width="120" height="120" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="1" rx="3"/>
  <text x="810" y="260" class="component-label">STM32微控制器</text>
  <text x="810" y="280" class="component-label">SPI接口</text>
  
  <!-- 外围器件 - 滤波电容 -->
  <rect x="450" y="550" width="40" height="20" fill="#fff" stroke="#333" rx="2"/>
  <text x="470" y="565" class="component-label">C1</text>
  <text x="470" y="580" class="value-label">100nF</text>
  
  <rect x="520" y="550" width="40" height="20" fill="#fff" stroke="#333" rx="2"/>
  <text x="540" y="565" class="component-label">C2</text>
  <text x="540" y="580" class="value-label">10μF</text>
  
  <rect x="590" y="550" width="40" height="20" fill="#fff" stroke="#333" rx="2"/>
  <text x="610" y="565" class="component-label">C3</text>
  <text x="610" y="580" class="value-label">1μF</text>
  
  <!-- 连接线 -->
  <!-- 电源连接 -->
  <line x1="230" y1="255" x2="380" y2="257" class="power-line"/>
  <line x1="230" y1="295" x2="380" y2="287" class="ground-line"/>
  
  <!-- 参考电压连接 -->
  <line x1="230" y1="335" x2="380" y2="327" class="connection" stroke="#f57f17"/>
  <line x1="230" y1="295" x2="380" y2="357" class="ground-line"/>
  
  <!-- 信号输入连接 -->
  <line x1="220" y1="395" x2="380" y2="397" class="signal-line"/>
  <line x1="220" y1="435" x2="380" y2="427" class="signal-line"/>
  
  <!-- SPI连接 -->
  <line x1="620" y1="257" x2="750" y2="270" class="signal-line"/>
  <line x1="620" y1="287" x2="750" y2="290" class="signal-line"/>
  <line x1="620" y1="317" x2="750" y2="310" class="signal-line"/>
  <line x1="620" y1="347" x2="750" y2="330" class="signal-line"/>
  
  <!-- 电容到地连接 -->
  <line x1="470" y1="570" x2="470" y2="590" class="ground-line"/>
  <line x1="540" y1="570" x2="540" y2="590" class="ground-line"/>
  <line x1="610" y1="570" x2="610" y2="590" class="ground-line"/>
  
  <!-- 地线符号 -->
  <g transform="translate(470,590)">
    <line x1="-8" y1="0" x2="8" y2="0" stroke="#388e3c" stroke-width="2"/>
    <line x1="-5" y1="3" x2="5" y2="3" stroke="#388e3c" stroke-width="2"/>
    <line x1="-2" y1="6" x2="2" y2="6" stroke="#388e3c" stroke-width="2"/>
  </g>
  
  <g transform="translate(540,590)">
    <line x1="-8" y1="0" x2="8" y2="0" stroke="#388e3c" stroke-width="2"/>
    <line x1="-5" y1="3" x2="5" y2="3" stroke="#388e3c" stroke-width="2"/>
    <line x1="-2" y1="6" x2="2" y2="6" stroke="#388e3c" stroke-width="2"/>
  </g>
  
  <g transform="translate(610,590)">
    <line x1="-8" y1="0" x2="8" y2="0" stroke="#388e3c" stroke-width="2"/>
    <line x1="-5" y1="3" x2="5" y2="3" stroke="#388e3c" stroke-width="2"/>
    <line x1="-2" y1="6" x2="2" y2="6" stroke="#388e3c" stroke-width="2"/>
  </g>
  
  <!-- 技术参数标注 -->
  <text x="100" y="100" class="component-label">技术参数：</text>
  <text x="100" y="120" class="value-label">• VDD: +3.3V ±5%, 电流消耗: &lt;10mA</text>
  <text x="100" y="135" class="value-label">• VREF: +2.5V ±0.1%, 温度系数: &lt;10ppm/°C</text>
  <text x="100" y="150" class="value-label">• 输入电压: 0-VREF, 分辨率: 24位</text>
  <text x="100" y="165" class="value-label">• SPI模式: Mode 0, 时钟频率: 最大10MHz</text>
</svg>
