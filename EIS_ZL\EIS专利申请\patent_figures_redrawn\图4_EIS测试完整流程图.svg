<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="1700" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", <PERSON>l; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 11px; text-anchor: middle; }
      .decision-text { font-family: "Microsoft YaHei", <PERSON>l; font-size: 10px; text-anchor: middle; }
      .branch-text { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; fill: #666; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .loop-connection { stroke: #666; stroke-width: 1.5; fill: none; marker-end: url(#arrowhead); stroke-dasharray: 5,5; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="400" y="30" class="title">图4 EIS测试完整流程图</text>
  
  <!-- 开始 -->
  <ellipse cx="400" cy="80" rx="60" ry="25" fill="#c8e6c9" stroke="#333" stroke-width="2"/>
  <text x="400" y="87" class="step-text">开始</text>
  
  <!-- 系统初始化 -->
  <rect x="320" y="130" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="155" class="step-text">系统初始化</text>
  
  <!-- 设备自检 -->
  <rect x="320" y="200" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="225" class="step-text">设备自检</text>
  
  <!-- 自检通过判断 -->
  <polygon points="400,270 450,300 400,330 350,300" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="400" y="305" class="decision-text">自检通过?</text>
  
  <!-- 错误处理分支 -->
  <rect x="520" y="280" width="120" height="40" fill="#ffcdd2" stroke="#333" stroke-width="2" rx="5"/>
  <text x="580" y="305" class="step-text">显示错误信息</text>
  
  <!-- 错误结束 -->
  <ellipse cx="580" cy="370" rx="60" ry="25" fill="#ffcdd2" stroke="#333" stroke-width="2"/>
  <text x="580" y="377" class="step-text">结束</text>
  
  <!-- 设置测试参数 -->
  <rect x="320" y="380" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="405" class="step-text">设置测试参数</text>
  
  <!-- 频率范围设置 -->
  <rect x="320" y="450" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="475" class="step-text">频率范围设置</text>
  
  <!-- 扫描点数设置 -->
  <rect x="320" y="520" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="545" class="step-text">扫描点数设置</text>
  
  <!-- 激励电流设置 -->
  <rect x="320" y="590" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="615" class="step-text">激励电流设置</text>
  
  <!-- 开始频率扫描 -->
  <rect x="320" y="660" width="160" height="40" fill="#e1f5fe" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="685" class="step-text">开始频率扫描</text>
  
  <!-- 设置当前频率 -->
  <rect x="320" y="730" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="755" class="step-text">设置当前频率</text>
  
  <!-- 输出激励信号 -->
  <rect x="320" y="800" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="825" class="step-text">输出激励信号</text>
  
  <!-- 采集响应信号 -->
  <rect x="320" y="870" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="895" class="step-text">采集响应信号</text>
  
  <!-- 计算阻抗值 -->
  <rect x="320" y="940" width="160" height="40" fill="#e1f5fe" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="965" class="step-text">计算阻抗值</text>
  
  <!-- 存储测量数据 -->
  <rect x="320" y="1010" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1035" class="step-text">存储测量数据</text>
  
  <!-- 是否为最后频率点判断 -->
  <polygon points="400,1080 450,1110 400,1140 350,1110" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="400" y="1110" class="decision-text">最后频率点?</text>
  <text x="400" y="1125" class="decision-text"></text>
  
  <!-- 切换到下一频率 -->
  <rect x="120" y="1090" width="140" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="190" y="1115" class="step-text">切换到下一频率</text>
  
  <!-- 数据预处理 -->
  <rect x="520" y="1090" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="600" y="1115" class="step-text">数据预处理</text>
  
  <!-- 多维参数提取 -->
  <rect x="320" y="1160" width="160" height="40" fill="#e1f5fe" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1185" class="step-text">多维参数提取</text>

  <!-- 九档智能分组 -->
  <rect x="320" y="1230" width="160" height="40" fill="#e1f5fe" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1255" class="step-text">九档智能分组</text>

  <!-- 增益自适应调节 -->
  <rect x="320" y="1300" width="160" height="40" fill="#e1f5fe" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1325" class="step-text">增益自适应调节</text>

  <!-- 生成测试报告 -->
  <rect x="320" y="1370" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1395" class="step-text">生成测试报告</text>

  <!-- 保存测试结果 -->
  <rect x="320" y="1440" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1465" class="step-text">保存测试结果</text>

  <!-- 显示奈奎斯特曲线 -->
  <rect x="320" y="1510" width="160" height="40" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="400" y="1535" class="step-text">显示奈奎斯特曲线</text>

  <!-- 继续测试判断 -->
  <polygon points="400,1580 450,1610 400,1640 350,1610" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="400" y="1615" class="decision-text">继续测试?</text>

  <!-- 正常结束 -->
  <ellipse cx="580" cy="1610" rx="60" ry="25" fill="#c8e6c9" stroke="#333" stroke-width="2"/>
  <text x="580" y="1617" class="step-text">结束</text>

  <!-- 连接线 -->
  <!-- 主流程连接 -->
  <line x1="400" y1="105" x2="400" y2="130" class="connection"/>
  <line x1="400" y1="170" x2="400" y2="200" class="connection"/>
  <line x1="400" y1="240" x2="400" y2="270" class="connection"/>

  <!-- 自检失败分支 -->
  <line x1="450" y1="300" x2="520" y2="300" class="connection"/>
  <text x="485" y="295" class="branch-text">否</text>
  <line x1="580" y1="320" x2="580" y2="345" class="connection"/>

  <!-- 自检成功继续 -->
  <line x1="400" y1="330" x2="400" y2="380" class="connection"/>
  <text x="415" y="355" class="branch-text">是</text>

  <!-- 参数设置流程 -->
  <line x1="400" y1="420" x2="400" y2="450" class="connection"/>
  <line x1="400" y1="490" x2="400" y2="520" class="connection"/>
  <line x1="400" y1="560" x2="400" y2="590" class="connection"/>
  <line x1="400" y1="630" x2="400" y2="660" class="connection"/>

  <!-- 测试执行流程 -->
  <line x1="400" y1="700" x2="400" y2="730" class="connection"/>
  <line x1="400" y1="770" x2="400" y2="800" class="connection"/>
  <line x1="400" y1="840" x2="400" y2="870" class="connection"/>
  <line x1="400" y1="910" x2="400" y2="940" class="connection"/>
  <line x1="400" y1="980" x2="400" y2="1010" class="connection"/>
  <line x1="400" y1="1050" x2="400" y2="1080" class="connection"/>

  <!-- 频率循环 -->
  <line x1="350" y1="1110" x2="260" y2="1110" class="connection"/>
  <text x="305" y="1105" class="branch-text">否</text>
  <line x1="190" y1="1090" x2="190" y2="760" class="loop-connection"/>
  <line x1="190" y1="760" x2="320" y2="760" class="loop-connection"/>

  <!-- 数据处理流程 -->
  <line x1="450" y1="1110" x2="520" y2="1110" class="connection"/>
  <text x="485" y="1105" class="branch-text">是</text>

  <!-- 数据预处理到多维参数提取 -->
  <line x1="600" y1="1130" x2="400" y2="1160" class="connection"/>

  <!-- 算法处理流程 -->
  <line x1="400" y1="1200" x2="400" y2="1230" class="connection"/>
  <line x1="400" y1="1270" x2="400" y2="1300" class="connection"/>
  <line x1="400" y1="1340" x2="400" y2="1370" class="connection"/>

  <!-- 结果输出流程 -->
  <line x1="400" y1="1410" x2="400" y2="1440" class="connection"/>
  <line x1="400" y1="1480" x2="400" y2="1510" class="connection"/>
  <line x1="400" y1="1550" x2="400" y2="1580" class="connection"/>

  <!-- 继续测试循环 -->
  <line x1="350" y1="1610" x2="100" y2="1610" class="loop-connection"/>
  <text x="225" y="1605" class="branch-text">是</text>
  <line x1="100" y1="1610" x2="100" y2="405" class="loop-connection"/>
  <line x1="100" y1="405" x2="320" y2="405" class="loop-connection"/>

  <!-- 结束流程 -->
  <line x1="450" y1="1610" x2="520" y2="1610" class="connection"/>
  <text x="485" y="1605" class="branch-text">否</text>
</svg>
