<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", <PERSON>l; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 11px; text-anchor: middle; }
      .detail-text { font-family: "Microsoft YaHei", <PERSON>l; font-size: 9px; text-anchor: start; fill: #666; }
      .formula-text { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; font-style: italic; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-flow { stroke: #1976d2; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="450" y="30" class="title">图6 多维参数提取算法流程图</text>
  
  <!-- 原始数据输入 -->
  <rect x="350" y="70" width="200" height="50" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="450" y="95" class="step-text">原始EIS数据输入</text>
  <text x="450" y="110" class="detail-text">Z(ω) = Z'(ω) + jZ''(ω)</text>
  
  <!-- 数据预处理 -->
  <rect x="350" y="160" width="200" height="50" fill="#fff" stroke="#333" stroke-width="2" rx="5"/>
  <text x="450" y="185" class="step-text">数据预处理</text>
  <text x="450" y="200" class="detail-text">噪声滤除、异常值检测</text>
  
  <!-- 频域分析 -->
  <rect x="100" y="250" width="180" height="60" fill="#fff3e0" stroke="#ff6f00" stroke-width="2" rx="5"/>
  <text x="190" y="275" class="step-text">频域分析</text>
  <text x="190" y="290" class="detail-text">高频区域分析</text>
  <text x="190" y="305" class="detail-text">Rs提取</text>
  
  <!-- 时域分析 -->
  <rect x="350" y="250" width="180" height="60" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="440" y="275" class="step-text">时域分析</text>
  <text x="440" y="290" class="detail-text">低频区域分析</text>
  <text x="440" y="305" class="detail-text">Rp、Cp提取</text>
  
  <!-- 复数域分析 -->
  <rect x="600" y="250" width="180" height="60" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="690" y="275" class="step-text">复数域分析</text>
  <text x="690" y="290" class="detail-text">相位角分析</text>
  <text x="690" y="305" class="detail-text">Q值提取</text>
  
  <!-- Rs参数提取 -->
  <rect x="50" y="360" width="140" height="50" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="120" y="380" class="step-text">Rs参数提取</text>
  <text x="120" y="395" class="formula-text">Rs = Z'(f→∞)</text>
  
  <!-- Rp参数提取 -->
  <rect x="220" y="360" width="140" height="50" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="290" y="380" class="step-text">Rp参数提取</text>
  <text x="290" y="395" class="formula-text">Rp = Z'(f→0) - Rs</text>
  
  <!-- Cp参数提取 -->
  <rect x="390" y="360" width="140" height="50" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="460" y="380" class="step-text">Cp参数提取</text>
  <text x="460" y="395" class="formula-text">Cp = 1/(2πfRp)</text>
  
  <!-- Q值提取 -->
  <rect x="560" y="360" width="140" height="50" fill="#ffebee" stroke="#d32f2f" stroke-width="1" rx="3"/>
  <text x="630" y="380" class="step-text">Q值提取</text>
  <text x="630" y="395" class="formula-text">Q = |Z|/Rs</text>
  
  <!-- 等效电路拟合 -->
  <rect x="300" y="460" width="200" height="60" fill="#e1f5fe" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="400" y="485" class="step-text">等效电路拟合</text>
  <text x="400" y="500" class="detail-text">Randles电路模型</text>
  <text x="400" y="515" class="detail-text">最小二乘法拟合</text>
  
  <!-- 参数验证 -->
  <rect x="100" y="560" width="150" height="50" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="5"/>
  <text x="175" y="585" class="step-text">参数验证</text>
  <text x="175" y="600" class="detail-text">拟合度检验</text>
  
  <!-- 误差分析 -->
  <rect x="300" y="560" width="150" height="50" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="5"/>
  <text x="375" y="585" class="step-text">误差分析</text>
  <text x="375" y="600" class="detail-text">残差计算</text>
  
  <!-- 参数优化 -->
  <rect x="500" y="560" width="150" height="50" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="5"/>
  <text x="575" y="585" class="step-text">参数优化</text>
  <text x="575" y="600" class="detail-text">迭代优化</text>
  
  <!-- 多维参数输出 -->
  <rect x="250" y="660" width="300" height="80" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="400" y="685" class="step-text">多维参数输出</text>
  <text x="400" y="705" class="detail-text">Rs: 串联电阻 (mΩ)</text>
  <text x="400" y="720" class="detail-text">Rp: 并联电阻 (mΩ)</text>
  <text x="400" y="735" class="detail-text">Cp: 并联电容 (F)</text>
  
  <!-- 参数质量评估 -->
  <rect x="300" y="780" width="200" height="60" fill="#fce4ec" stroke="#e91e63" stroke-width="2" rx="5"/>
  <text x="400" y="805" class="step-text">参数质量评估</text>
  <text x="400" y="820" class="detail-text">置信度计算</text>
  <text x="400" y="835" class="detail-text">可靠性评级</text>
  
  <!-- 连接线 -->
  <!-- 主流程 -->
  <line x1="450" y1="120" x2="450" y2="160" class="connection"/>
  
  <!-- 分支到三个分析模块 -->
  <line x1="400" y1="210" x2="190" y2="250" class="data-flow"/>
  <line x1="450" y1="210" x2="440" y2="250" class="data-flow"/>
  <line x1="500" y1="210" x2="690" y2="250" class="data-flow"/>
  
  <!-- 分析模块到参数提取 -->
  <line x1="190" y1="310" x2="120" y2="360" class="connection"/>
  <line x1="190" y1="310" x2="290" y2="360" class="connection"/>
  <line x1="440" y1="310" x2="460" y2="360" class="connection"/>
  <line x1="690" y1="310" x2="630" y2="360" class="connection"/>
  
  <!-- 参数提取到等效电路拟合 -->
  <line x1="120" y1="410" x2="350" y2="460" class="connection"/>
  <line x1="290" y1="410" x2="370" y2="460" class="connection"/>
  <line x1="460" y1="410" x2="430" y2="460" class="connection"/>
  <line x1="630" y1="410" x2="450" y2="460" class="connection"/>
  
  <!-- 等效电路拟合到验证模块 -->
  <line x1="350" y1="520" x2="175" y2="560" class="connection"/>
  <line x1="400" y1="520" x2="375" y2="560" class="connection"/>
  <line x1="450" y1="520" x2="575" y2="560" class="connection"/>
  
  <!-- 验证模块到输出 -->
  <line x1="175" y1="610" x2="350" y2="660" class="connection"/>
  <line x1="375" y1="610" x2="400" y2="660" class="connection"/>
  <line x1="575" y1="610" x2="450" y2="660" class="connection"/>
  
  <!-- 输出到质量评估 -->
  <line x1="400" y1="740" x2="400" y2="780" class="connection"/>
  
  <!-- 算法特点说明 -->
  <rect x="50" y="880" width="800" height="110" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="3"/>
  <text x="450" y="905" class="step-text">算法特点</text>

  <!-- 左列特点 -->
  <text x="80" y="930" class="detail-text">• 多频域并行分析，提高参数提取精度</text>
  <text x="80" y="945" class="detail-text">• 自适应等效电路模型选择</text>
  <text x="80" y="960" class="detail-text">• 实时误差反馈和参数优化</text>

  <!-- 右列特点 -->
  <text x="450" y="930" class="detail-text">• 支持多种电池类型的参数提取</text>
  <text x="450" y="945" class="detail-text">• 温度补偿和老化修正</text>
  <text x="450" y="960" class="detail-text">• 参数置信度评估</text>
</svg>
