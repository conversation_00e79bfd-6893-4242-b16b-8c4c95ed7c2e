<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: "Microsoft YaHei", Arial; font-size: 11px; text-anchor: middle; }
      .decision-text { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .grade-text { font-family: "Microsoft YaHei", Arial; font-size: 12px; text-anchor: middle; font-weight: bold; }
      .param-text { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; fill: #666; }
      .branch-text { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; fill: #333; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .grade-a { fill: #c8e6c9; stroke: #2e7d32; stroke-width: 2; }
      .grade-b { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      .grade-c { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">图7 九档智能分组决策流程图</text>
  
  <!-- 输入参数 -->
  <rect x="500" y="70" width="200" height="50" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="600" y="95" class="step-text">输入参数</text>
  <text x="600" y="110" class="param-text">Rs, Rp值</text>
  
  <!-- Rs分类判断 -->
  <polygon points="600,150 680,180 600,210 520,180" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="600" y="175" class="decision-text">Rs分类</text>
  <text x="600" y="190" class="decision-text">判断</text>
  
  <!-- Rs低档 -->
  <rect x="200" y="250" width="120" height="40" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" rx="3"/>
  <text x="260" y="270" class="step-text">Rs低档</text>
  <text x="260" y="285" class="param-text">Rs ≤ 15mΩ</text>
  
  <!-- Rs中档 -->
  <rect x="540" y="250" width="120" height="40" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="3"/>
  <text x="600" y="270" class="step-text">Rs中档</text>
  <text x="600" y="285" class="param-text">15 < Rs ≤ 25mΩ</text>
  
  <!-- Rs高档 -->
  <rect x="880" y="250" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3"/>
  <text x="940" y="270" class="step-text">Rs高档</text>
  <text x="940" y="285" class="param-text">Rs > 25mΩ</text>
  
  <!-- Rp分类判断 - 低档Rs分支 -->
  <polygon points="260,330 320,350 260,370 200,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="260" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 中档Rs分支 -->
  <polygon points="600,330 660,350 600,370 540,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="600" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 高档Rs分支 -->
  <polygon points="940,330 1000,350 940,370 880,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="940" y="350" class="decision-text">Rp判断</text>
  
  <!-- 九个等级输出 -->
  <!-- A级电池 (Rs低, Rp低) -->
  <rect x="80" y="420" width="100" height="60" class="grade-a" rx="5"/>
  <text x="130" y="445" class="grade-text">A1级</text>
  <text x="130" y="460" class="param-text">Rs≤15, Rp≤30</text>
  <text x="130" y="475" class="param-text">优秀电池</text>
  
  <!-- A级电池 (Rs低, Rp中) -->
  <rect x="210" y="420" width="100" height="60" class="grade-a" rx="5"/>
  <text x="260" y="445" class="grade-text">A2级</text>
  <text x="260" y="460" class="param-text">Rs≤15, 30<Rp≤60</text>
  <text x="260" y="475" class="param-text">良好电池</text>
  
  <!-- B级电池 (Rs低, Rp高) -->
  <rect x="340" y="420" width="100" height="60" class="grade-b" rx="5"/>
  <text x="390" y="445" class="grade-text">B1级</text>
  <text x="390" y="460" class="param-text">Rs≤15, Rp>60</text>
  <text x="390" y="475" class="param-text">一般电池</text>
  
  <!-- A级电池 (Rs中, Rp低) -->
  <rect x="470" y="420" width="100" height="60" class="grade-a" rx="5"/>
  <text x="520" y="445" class="grade-text">A3级</text>
  <text x="520" y="460" class="param-text">15<Rs≤25, Rp≤30</text>
  <text x="520" y="475" class="param-text">良好电池</text>
  
  <!-- B级电池 (Rs中, Rp中) -->
  <rect x="600" y="420" width="100" height="60" class="grade-b" rx="5"/>
  <text x="650" y="445" class="grade-text">B2级</text>
  <text x="650" y="460" class="param-text">15<Rs≤25, 30<Rp≤60</text>
  <text x="650" y="475" class="param-text">一般电池</text>
  
  <!-- B级电池 (Rs中, Rp高) -->
  <rect x="730" y="420" width="100" height="60" class="grade-b" rx="5"/>
  <text x="780" y="445" class="grade-text">B3级</text>
  <text x="780" y="460" class="param-text">15<Rs≤25, Rp>60</text>
  <text x="780" y="475" class="param-text">较差电池</text>
  
  <!-- B级电池 (Rs高, Rp低) -->
  <rect x="860" y="420" width="100" height="60" class="grade-b" rx="5"/>
  <text x="910" y="445" class="grade-text">B4级</text>
  <text x="910" y="460" class="param-text">Rs>25, Rp≤30</text>
  <text x="910" y="475" class="param-text">较差电池</text>
  
  <!-- C级电池 (Rs高, Rp中) -->
  <rect x="990" y="420" width="100" height="60" class="grade-c" rx="5"/>
  <text x="1040" y="445" class="grade-text">C1级</text>
  <text x="1040" y="460" class="param-text">Rs>25, 30<Rp≤60</text>
  <text x="1040" y="475" class="param-text">劣质电池</text>
  
  <!-- C级电池 (Rs高, Rp高) -->
  <rect x="1120" y="420" width="100" height="60" class="grade-c" rx="5"/>
  <text x="1170" y="445" class="grade-text">C2级</text>
  <text x="1170" y="460" class="param-text">Rs>25, Rp>60</text>
  <text x="1170" y="475" class="param-text">废品电池</text>
  
  <!-- 连接线 -->
  <!-- 输入到Rs判断 -->
  <line x1="600" y1="120" x2="600" y2="150" class="connection"/>
  
  <!-- Rs判断到三个分支 -->
  <line x1="550" y1="180" x2="260" y2="250" class="connection"/>
  <text x="400" y="215" class="branch-text">Rs≤15</text>
  
  <line x1="600" y1="210" x2="600" y2="250" class="connection"/>
  <text x="615" y="230" class="branch-text">15<Rs≤25</text>
  
  <line x1="650" y1="180" x2="940" y2="250" class="connection"/>
  <text x="795" y="215" class="branch-text">Rs>25</text>
  
  <!-- Rs分支到Rp判断 -->
  <line x1="260" y1="290" x2="260" y2="330" class="connection"/>
  <line x1="600" y1="290" x2="600" y2="330" class="connection"/>
  <line x1="940" y1="290" x2="940" y2="330" class="connection"/>
  
  <!-- Rp判断到最终等级 - 左分支 -->
  <line x1="220" y1="350" x2="130" y2="420" class="connection"/>
  <text x="175" y="385" class="branch-text">Rp≤30</text>
  
  <line x1="260" y1="370" x2="260" y2="420" class="connection"/>
  <text x="275" y="395" class="branch-text">30<Rp≤60</text>
  
  <line x1="300" y1="350" x2="390" y2="420" class="connection"/>
  <text x="345" y="385" class="branch-text">Rp>60</text>
  
  <!-- Rp判断到最终等级 - 中分支 -->
  <line x1="560" y1="350" x2="520" y2="420" class="connection"/>
  <text x="540" y="385" class="branch-text">Rp≤30</text>
  
  <line x1="600" y1="370" x2="650" y2="420" class="connection"/>
  <text x="625" y="395" class="branch-text">30<Rp≤60</text>
  
  <line x1="640" y1="350" x2="780" y2="420" class="connection"/>
  <text x="710" y="385" class="branch-text">Rp>60</text>
  
  <!-- Rp判断到最终等级 - 右分支 -->
  <line x1="900" y1="350" x2="910" y2="420" class="connection"/>
  <text x="905" y="385" class="branch-text">Rp≤30</text>
  
  <line x1="940" y1="370" x2="1040" y2="420" class="connection"/>
  <text x="990" y="395" class="branch-text">30<Rp≤60</text>
  
  <line x1="980" y1="350" x2="1170" y2="420" class="connection"/>
  <text x="1075" y="385" class="branch-text">Rp>60</text>
  
  <!-- 分组策略说明 -->
  <rect x="50" y="520" width="1100" height="120" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="600" y="545" class="step-text">九档智能分组策略</text>
  
  <text x="70" y="570" class="param-text">A级电池 (4档): 优秀-良好电池，可直接使用或优先配组</text>
  <text x="70" y="585" class="param-text">• A1级: Rs≤15mΩ, Rp≤30mΩ - 性能最优，适合高端应用</text>
  <text x="70" y="600" class="param-text">• A2级: Rs≤15mΩ, 30<Rp≤60mΩ - 性能良好，适合一般应用</text>
  <text x="70" y="615" class="param-text">• A3级: 15<Rs≤25mΩ, Rp≤30mΩ - 内阻稍高但极化小</text>
  
  <text x="600" y="570" class="param-text">B级电池 (4档): 一般-较差电池，需要筛选配组</text>
  <text x="600" y="585" class="param-text">• B1级: Rs≤15mΩ, Rp>60mΩ - 内阻低但极化大</text>
  <text x="600" y="600" class="param-text">• B2级: 15<Rs≤25mΩ, 30<Rp≤60mΩ - 中等性能</text>
  <text x="600" y="615" class="param-text">• B3级: 15<Rs≤25mΩ, Rp>60mΩ - 性能较差</text>
  <text x="600" y="630" class="param-text">• B4级: Rs>25mΩ, Rp≤30mΩ - 内阻高但极化小</text>
  
  <!-- 分组结果统计 -->
  <rect x="200" y="680" width="800" height="80" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="600" y="705" class="step-text">分组结果统计与应用建议</text>
  <text x="220" y="730" class="param-text">A级电池: 适合高性能应用，建议同级配组</text>
  <text x="220" y="745" class="param-text">B级电池: 适合一般应用，可混合配组或降级使用</text>
  
  <text x="600" y="730" class="param-text">C级电池: 性能不达标，建议回收处理</text>
  <text x="600" y="745" class="param-text">分组精度: >95%, 一致性提升: >30%</text>
</svg>
