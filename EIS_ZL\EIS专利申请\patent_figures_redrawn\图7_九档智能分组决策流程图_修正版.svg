<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: "Microsoft YaHei", Arial; font-size: 11px; text-anchor: middle; }
      .decision-text { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .grade-text { font-family: "Microsoft YaHei", Arial; font-size: 12px; text-anchor: middle; font-weight: bold; }
      .param-text { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: start; fill: #333; }
      .param-text-center { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; fill: #666; }
      .branch-text { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 9px; text-anchor: middle; fill: #333; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .grade-a { fill: #c8e6c9; stroke: #2e7d32; stroke-width: 2; }
      .grade-b { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      .grade-c { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="700" y="30" class="title">图7 九档智能分组决策流程图</text>

  <!-- 输入参数 -->
  <rect x="600" y="70" width="200" height="50" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="700" y="95" class="step-text">输入参数</text>
  <text x="700" y="110" class="param-text-center">Rs, Rp阻抗值</text>

  <!-- Rs分类判断 -->
  <polygon points="700,150 780,180 700,210 620,180" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="700" y="175" class="decision-text">Rs分类</text>
  <text x="700" y="190" class="decision-text">判断</text>
  
  <!-- Rs低档 -->
  <rect x="200" y="250" width="120" height="40" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" rx="3"/>
  <text x="260" y="270" class="step-text">Rs低档</text>
  <text x="260" y="285" class="param-text">Rs ≤ 15mΩ</text>
  
  <!-- Rs中档 -->
  <rect x="540" y="250" width="120" height="40" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="3"/>
  <text x="600" y="270" class="step-text">Rs中档</text>
  <text x="600" y="285" class="param-text">15 &lt; Rs ≤ 25mΩ</text>
  
  <!-- Rs高档 -->
  <rect x="880" y="250" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3"/>
  <text x="940" y="270" class="step-text">Rs高档</text>
  <text x="940" y="285" class="param-text">Rs > 25mΩ</text>
  
  <!-- Rp分类判断 - 低档Rs分支 -->
  <polygon points="260,330 320,350 260,370 200,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="260" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 中档Rs分支 -->
  <polygon points="600,330 660,350 600,370 540,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="600" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 高档Rs分支 -->
  <polygon points="940,330 1000,350 940,370 880,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="940" y="350" class="decision-text">Rp判断</text>
  
  <!-- 第一层：连接线（最底层，不压框框） -->
  <!-- 输入到Rs判断 -->
  <line x1="700" y1="120" x2="700" y2="150" class="connection"/>

  <!-- Rs判断到三个分支 -->
  <line x1="650" y1="180" x2="300" y2="250" class="connection"/>
  <line x1="700" y1="210" x2="700" y2="250" class="connection"/>
  <line x1="750" y1="180" x2="1050" y2="250" class="connection"/>

  <!-- Rs分支到Rp判断 -->
  <line x1="300" y1="290" x2="300" y2="330" class="connection"/>
  <line x1="700" y1="290" x2="700" y2="330" class="connection"/>
  <line x1="1050" y1="290" x2="1050" y2="330" class="connection"/>

  <!-- Rp判断到最终等级 - 左分支(Rs1) -->
  <line x1="260" y1="350" x2="180" y2="420" class="connection"/>
  <line x1="300" y1="370" x2="300" y2="420" class="connection"/>
  <line x1="340" y1="350" x2="420" y2="420" class="connection"/>

  <!-- Rp判断到最终等级 - 中分支(Rs2) -->
  <line x1="660" y1="350" x2="580" y2="420" class="connection"/>
  <line x1="700" y1="370" x2="700" y2="420" class="connection"/>
  <line x1="740" y1="350" x2="820" y2="420" class="connection"/>

  <!-- Rp判断到最终等级 - 右分支(Rs3) -->
  <line x1="1010" y1="350" x2="980" y2="420" class="connection"/>
  <line x1="1050" y1="370" x2="1100" y2="420" class="connection"/>
  <line x1="1090" y1="350" x2="1220" y2="420" class="connection"/>

  <!-- 第二层：Rs分类框 -->
  <!-- Rs1档 (低) -->
  <rect x="240" y="250" width="120" height="40" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" rx="3"/>
  <text x="300" y="270" class="step-text">Rs1档</text>
  <text x="300" y="285" class="param-text-center">Rs ≤ 15mΩ</text>

  <!-- Rs2档 (中) -->
  <rect x="640" y="250" width="120" height="40" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="3"/>
  <text x="700" y="270" class="step-text">Rs2档</text>
  <text x="700" y="285" class="param-text-center">15 &lt; Rs ≤ 25mΩ</text>

  <!-- Rs3档 (高) -->
  <rect x="990" y="250" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3"/>
  <text x="1050" y="270" class="step-text">Rs3档</text>
  <text x="1050" y="285" class="param-text-center">Rs > 25mΩ</text>

  <!-- 第三层：Rp分类判断框 -->
  <!-- Rp分类判断 - Rs1分支 -->
  <polygon points="300,330 360,350 300,370 240,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="300" y="350" class="decision-text">Rp判断</text>

  <!-- Rp分类判断 - Rs2分支 -->
  <polygon points="700,330 760,350 700,370 640,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="700" y="350" class="decision-text">Rp判断</text>

  <!-- Rp分类判断 - Rs3分支 -->
  <polygon points="1050,330 1110,350 1050,370 990,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="1050" y="350" class="decision-text">Rp判断</text>

  <!-- 第四层：九个等级输出框（3×3矩阵布局，无重叠） -->
  <!-- Rs1档组合 -->
  <rect x="120" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="180" y="445" class="grade-text">1-1级</text>
  <text x="180" y="460" class="param-text-center">Rs1+Rp1</text>
  <text x="180" y="475" class="param-text-center">优秀电池</text>

  <rect x="260" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="320" y="445" class="grade-text">1-2级</text>
  <text x="320" y="460" class="param-text-center">Rs1+Rp2</text>
  <text x="320" y="475" class="param-text-center">良好电池</text>

  <rect x="400" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="460" y="445" class="grade-text">1-3级</text>
  <text x="460" y="460" class="param-text-center">Rs1+Rp3</text>
  <text x="460" y="475" class="param-text-center">一般电池</text>

  <!-- Rs2档组合 -->
  <rect x="540" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="600" y="445" class="grade-text">2-1级</text>
  <text x="600" y="460" class="param-text-center">Rs2+Rp1</text>
  <text x="600" y="475" class="param-text-center">良好电池</text>

  <rect x="680" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="740" y="445" class="grade-text">2-2级</text>
  <text x="740" y="460" class="param-text-center">Rs2+Rp2</text>
  <text x="740" y="475" class="param-text-center">一般电池</text>

  <rect x="820" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="880" y="445" class="grade-text">2-3级</text>
  <text x="880" y="460" class="param-text-center">Rs2+Rp3</text>
  <text x="880" y="475" class="param-text-center">较差电池</text>

  <!-- Rs3档组合 -->
  <rect x="960" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="1020" y="445" class="grade-text">3-1级</text>
  <text x="1020" y="460" class="param-text-center">Rs3+Rp1</text>
  <text x="1020" y="475" class="param-text-center">较差电池</text>

  <rect x="1100" y="420" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1160" y="445" class="grade-text">3-2级</text>
  <text x="1160" y="460" class="param-text-center">Rs3+Rp2</text>
  <text x="1160" y="475" class="param-text-center">劣质电池</text>

  <rect x="1240" y="420" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1300" y="445" class="grade-text">3-3级</text>
  <text x="1300" y="460" class="param-text-center">Rs3+Rp3</text>
  <text x="1300" y="475" class="param-text-center">废品电池</text>

  <!-- 第五层：分支标签文字（最顶层，不被压盖） -->
  <text x="475" y="215" class="branch-text">Rs≤15</text>
  <text x="715" y="230" class="branch-text">15&lt;Rs≤25</text>
  <text x="875" y="215" class="branch-text">Rs&gt;25</text>

  <text x="215" y="385" class="branch-text">Rp1(≤30)</text>
  <text x="315" y="395" class="branch-text">Rp2(30-60)</text>
  <text x="385" y="385" class="branch-text">Rp3(&gt;60)</text>

  <text x="615" y="385" class="branch-text">Rp1(≤30)</text>
  <text x="715" y="395" class="branch-text">Rp2(30-60)</text>
  <text x="785" y="385" class="branch-text">Rp3(&gt;60)</text>

  <text x="965" y="385" class="branch-text">Rp1(≤30)</text>
  <text x="1115" y="395" class="branch-text">Rp2(30-60)</text>
  <text x="1185" y="385" class="branch-text">Rp3(&gt;60)</text>


  <!-- 分组策略说明框 -->
  <rect x="80" y="520" width="1240" height="160" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="700" y="545" class="step-text">九档智能分组策略 (3×3矩阵分档)</text>

  <!-- Rs分档说明 -->
  <text x="100" y="570" class="param-text">Rs分档标准 (串联阻抗):</text>
  <text x="100" y="585" class="param-text">• Rs1档: Rs ≤ 15mΩ (低阻抗)</text>
  <text x="100" y="600" class="param-text">• Rs2档: 15 &lt; Rs ≤ 25mΩ (中阻抗)</text>
  <text x="100" y="615" class="param-text">• Rs3档: Rs &gt; 25mΩ (高阻抗)</text>

  <!-- Rp分档说明 -->
  <text x="450" y="570" class="param-text">Rp分档标准 (极化阻抗):</text>
  <text x="450" y="585" class="param-text">• Rp1档: Rp ≤ 30mΩ (低极化)</text>
  <text x="450" y="600" class="param-text">• Rp2档: 30 &lt; Rp ≤ 60mΩ (中极化)</text>
  <text x="450" y="615" class="param-text">• Rp3档: Rp &gt; 60mΩ (高极化)</text>

  <!-- 组合分档说明 -->
  <text x="800" y="570" class="param-text">组合分档结果 (Rs×Rp):</text>
  <text x="800" y="585" class="param-text">• 1-1, 1-2, 2-1级: A级电池 (优秀-良好)</text>
  <text x="800" y="600" class="param-text">• 1-3, 2-2, 2-3, 3-1级: B级电池 (一般-较差)</text>
  <text x="800" y="615" class="param-text">• 3-2, 3-3级: C级电池 (劣质-废品)</text>

  <!-- 应用建议 -->
  <text x="100" y="645" class="param-text">应用建议:</text>
  <text x="100" y="660" class="param-text">• A级电池: 适合高性能应用，建议同级配组，可直接使用</text>
  <text x="800" y="645" class="param-text">分组精度: &gt;95%</text>
  <text x="800" y="660" class="param-text">一致性提升: &gt;30%</text>

  <!-- 分组结果统计框 -->
  <rect x="200" y="710" width="1000" height="80" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="700" y="735" class="step-text">3×3矩阵分档优势</text>
  <text x="220" y="760" class="param-text">• 全面覆盖: Rs和Rp两个关键参数的所有组合情况</text>
  <text x="220" y="775" class="param-text">• 逻辑清晰: 3×3=9档，每档对应明确的Rs-Rp组合</text>

  <text x="700" y="760" class="param-text">• 精度提升: 细分档位提高分组精度</text>
  <text x="700" y="775" class="param-text">• 易于实现: 简单的二维判断逻辑</text>
</svg>
