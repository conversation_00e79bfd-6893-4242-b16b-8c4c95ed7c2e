<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="950" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .step-text { font-family: "Microsoft YaHei", Arial; font-size: 11px; text-anchor: middle; }
      .decision-text { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .grade-text { font-family: "Microsoft YaHei", Arial; font-size: 12px; text-anchor: middle; font-weight: bold; }
      .param-text { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: start; fill: #333; }
      .param-text-center { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; fill: #666; }
      .branch-text { font-family: "Microsoft YaHei", <PERSON><PERSON>; font-size: 9px; text-anchor: middle; fill: #333; }
      .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .grade-a { fill: #c8e6c9; stroke: #2e7d32; stroke-width: 2; }
      .grade-b { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
      .grade-c { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="800" y="30" class="title">图7 九档智能分组决策流程图</text>

  <!-- 输入参数 -->
  <rect x="700" y="70" width="200" height="50" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="800" y="95" class="step-text">输入参数</text>
  <text x="800" y="110" class="param-text-center">Rs, Rct阻抗值</text>

  <!-- Rs分类判断 -->
  <polygon points="800,150 880,180 800,210 720,180" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="800" y="175" class="decision-text">Rs自适应</text>
  <text x="800" y="190" class="decision-text">三分档</text>
  
  <!-- Rs低档 -->
  <rect x="200" y="250" width="120" height="40" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" rx="3"/>
  <text x="260" y="270" class="step-text">Rs低档</text>
  <text x="260" y="285" class="param-text">Rs ≤ 15mΩ</text>
  
  <!-- Rs中档 -->
  <rect x="540" y="250" width="120" height="40" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="3"/>
  <text x="600" y="270" class="step-text">Rs中档</text>
  <text x="600" y="285" class="param-text">15 &lt; Rs ≤ 25mΩ</text>
  
  <!-- Rs高档 -->
  <rect x="880" y="250" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3"/>
  <text x="940" y="270" class="step-text">Rs高档</text>
  <text x="940" y="285" class="param-text">Rs > 25mΩ</text>
  
  <!-- Rp分类判断 - 低档Rs分支 -->
  <polygon points="260,330 320,350 260,370 200,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="260" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 中档Rs分支 -->
  <polygon points="600,330 660,350 600,370 540,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="600" y="350" class="decision-text">Rp判断</text>
  
  <!-- Rp分类判断 - 高档Rs分支 -->
  <polygon points="940,330 1000,350 940,370 880,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="940" y="350" class="decision-text">Rp判断</text>
  
  <!-- 第一层：连接线（最底层，不压框框） -->
  <!-- 输入到Rs判断 -->
  <line x1="700" y1="120" x2="700" y2="150" class="connection"/>

  <!-- Rs判断到三个分支 -->
  <line x1="750" y1="180" x2="300" y2="250" class="connection"/>
  <line x1="800" y1="210" x2="800" y2="250" class="connection"/>
  <line x1="850" y1="180" x2="1300" y2="250" class="connection"/>

  <!-- Rs分支到Rct判断 -->
  <line x1="300" y1="290" x2="300" y2="330" class="connection"/>
  <line x1="800" y1="290" x2="800" y2="330" class="connection"/>
  <line x1="1300" y1="290" x2="1300" y2="330" class="connection"/>

  <!-- Rct判断到最终等级 - 左分支(Rs低) -->
  <line x1="260" y1="350" x2="180" y2="420" class="connection"/>
  <line x1="300" y1="380" x2="300" y2="420" class="connection"/>
  <line x1="340" y1="350" x2="420" y2="420" class="connection"/>

  <!-- Rct判断到最终等级 - 中分支(Rs中) -->
  <line x1="760" y1="350" x2="680" y2="420" class="connection"/>
  <line x1="800" y1="380" x2="800" y2="420" class="connection"/>
  <line x1="840" y1="350" x2="920" y2="420" class="connection"/>

  <!-- Rct判断到最终等级 - 右分支(Rs高) -->
  <line x1="1260" y1="350" x2="1180" y2="420" class="connection"/>
  <line x1="1300" y1="380" x2="1300" y2="420" class="connection"/>
  <line x1="1340" y1="350" x2="1420" y2="420" class="connection"/>

  <!-- 第二层：Rs分类框 -->
  <!-- Rs1档 (低) -->
  <rect x="240" y="250" width="120" height="40" fill="#c8e6c9" stroke="#2e7d32" stroke-width="2" rx="3"/>
  <text x="300" y="270" class="step-text">Rs低档</text>
  <text x="300" y="285" class="param-text-center">Rs ≤ P33</text>

  <!-- Rs2档 (中) -->
  <rect x="740" y="250" width="120" height="40" fill="#fff9c4" stroke="#f57f17" stroke-width="2" rx="3"/>
  <text x="800" y="270" class="step-text">Rs中档</text>
  <text x="800" y="285" class="param-text-center">P33 &lt; Rs ≤ P67</text>

  <!-- Rs3档 (高) -->
  <rect x="1240" y="250" width="120" height="40" fill="#ffcdd2" stroke="#d32f2f" stroke-width="2" rx="3"/>
  <text x="1300" y="270" class="step-text">Rs高档</text>
  <text x="1300" y="285" class="param-text-center">Rs > P67</text>

  <!-- 第三层：Rct分类判断框 -->
  <!-- Rct分类判断 - Rs1分支 -->
  <polygon points="300,330 360,350 300,370 240,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="300" y="350" class="decision-text">Rct自适应</text>
  <text x="300" y="365" class="decision-text">三分档</text>

  <!-- Rct分类判断 - Rs2分支 -->
  <polygon points="800,330 860,350 800,370 740,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="800" y="350" class="decision-text">Rct自适应</text>
  <text x="800" y="365" class="decision-text">三分档</text>

  <!-- Rct分类判断 - Rs3分支 -->
  <polygon points="1300,330 1360,350 1300,370 1240,350" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="1300" y="350" class="decision-text">Rct自适应</text>
  <text x="1300" y="365" class="decision-text">三分档</text>

  <!-- 第四层：九个等级输出框（3×3矩阵布局，无重叠） -->
  <!-- Rs低档组合 -->
  <rect x="140" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="200" y="445" class="grade-text">1-1级</text>
  <text x="200" y="460" class="param-text-center">Rs低+Rct低</text>
  <text x="200" y="475" class="param-text-center">优秀电池</text>

  <rect x="280" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="340" y="445" class="grade-text">1-2级</text>
  <text x="340" y="460" class="param-text-center">Rs低+Rct中</text>
  <text x="340" y="475" class="param-text-center">良好电池</text>

  <rect x="420" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="480" y="445" class="grade-text">1-3级</text>
  <text x="480" y="460" class="param-text-center">Rs低+Rct高</text>
  <text x="480" y="475" class="param-text-center">一般电池</text>

  <!-- Rs中档组合 -->
  <rect x="640" y="420" width="120" height="60" class="grade-a" rx="5"/>
  <text x="700" y="445" class="grade-text">2-1级</text>
  <text x="700" y="460" class="param-text-center">Rs中+Rct低</text>
  <text x="700" y="475" class="param-text-center">良好电池</text>

  <rect x="780" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="840" y="445" class="grade-text">2-2级</text>
  <text x="840" y="460" class="param-text-center">Rs中+Rct中</text>
  <text x="840" y="475" class="param-text-center">一般电池</text>

  <rect x="920" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="980" y="445" class="grade-text">2-3级</text>
  <text x="980" y="460" class="param-text-center">Rs中+Rct高</text>
  <text x="980" y="475" class="param-text-center">较差电池</text>

  <!-- Rs高档组合 -->
  <rect x="1140" y="420" width="120" height="60" class="grade-b" rx="5"/>
  <text x="1200" y="445" class="grade-text">3-1级</text>
  <text x="1200" y="460" class="param-text-center">Rs高+Rct低</text>
  <text x="1200" y="475" class="param-text-center">较差电池</text>

  <rect x="1280" y="420" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1340" y="445" class="grade-text">3-2级</text>
  <text x="1340" y="460" class="param-text-center">Rs高+Rct中</text>
  <text x="1340" y="475" class="param-text-center">劣质电池</text>

  <rect x="1420" y="420" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1480" y="445" class="grade-text">3-3级</text>
  <text x="1480" y="460" class="param-text-center">Rs高+Rct高</text>
  <text x="1480" y="475" class="param-text-center">废品电池</text>

  <!-- 第五层：分支标签文字（最顶层，不被压盖） -->
  <text x="525" y="215" class="branch-text">Rs低档</text>
  <text x="815" y="230" class="branch-text">Rs中档</text>
  <text x="1075" y="215" class="branch-text">Rs高档</text>

  <text x="215" y="395" class="branch-text">Rct低</text>
  <text x="315" y="405" class="branch-text">Rct中</text>
  <text x="405" y="395" class="branch-text">Rct高</text>

  <text x="715" y="395" class="branch-text">Rct低</text>
  <text x="815" y="405" class="branch-text">Rct中</text>
  <text x="905" y="395" class="branch-text">Rct高</text>

  <text x="1215" y="395" class="branch-text">Rct低</text>
  <text x="1315" y="405" class="branch-text">Rct中</text>
  <text x="1405" y="395" class="branch-text">Rct高</text>


  <!-- 分组策略说明框 -->
  <rect x="80" y="520" width="1440" height="180" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="800" y="545" class="step-text">九档智能分组策略 (自适应3×3矩阵分档)</text>

  <!-- 自适应分档说明 -->
  <text x="100" y="570" class="param-text">自适应分档原理 (适用于所有电池类型):</text>
  <text x="100" y="585" class="param-text">• Rs分档: 根据样本Rs分布，P33和P67分位数自动确定阈值</text>
  <text x="100" y="600" class="param-text">• Rct分档: 根据样本Rct分布，P33和P67分位数自动确定阈值</text>
  <text x="100" y="615" class="param-text">• 无需预设固定阈值，适应不同电池品种的阻抗范围</text>

  <!-- Rct概念说明 -->
  <text x="800" y="570" class="param-text">Rct (电荷转移阻抗) 概念:</text>
  <text x="800" y="585" class="param-text">• Rct是电化学反应阻抗，反映电极反应活性</text>
  <text x="800" y="600" class="param-text">• Rct ≠ Rp，Rp包含Rct+扩散阻抗+其他极化阻抗</text>
  <text x="800" y="615" class="param-text">• Rct更能准确反映电池内部电化学性能</text>

  <!-- 分档示例 -->
  <text x="100" y="645" class="param-text">分档示例 (某款电池Rs: 0.15-0.25mΩ, Rct: 0.01-0.1mΩ):</text>
  <text x="100" y="660" class="param-text">• Rs低档: ≤0.18mΩ, Rs中档: 0.18-0.22mΩ, Rs高档: &gt;0.22mΩ</text>
  <text x="100" y="675" class="param-text">• Rct低档: ≤0.04mΩ, Rct中档: 0.04-0.07mΩ, Rct高档: &gt;0.07mΩ</text>

  <!-- 技术优势 -->
  <text x="800" y="645" class="param-text">技术优势:</text>
  <text x="800" y="660" class="param-text">• 自适应: 无需人工设定阈值，适用性强</text>
  <text x="800" y="675" class="param-text">• 精确性: 使用Rct替代Rp，分组更精确</text>

  <!-- 分组结果统计框 -->
  <rect x="200" y="730" width="1200" height="100" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="800" y="755" class="step-text">自适应3×3矩阵分档优势</text>
  <text x="220" y="780" class="param-text">• 全面覆盖: Rs和Rct两个关键参数的所有组合情况</text>
  <text x="220" y="795" class="param-text">• 自适应性: 根据样本分布自动确定分档阈值</text>
  <text x="220" y="810" class="param-text">• 通用性强: 适用于各种电池品种和阻抗范围</text>

  <text x="800" y="780" class="param-text">• 精度提升: 使用Rct替代Rp，分组更精确</text>
  <text x="800" y="795" class="param-text">• 逻辑清晰: 3×3=9档，每档对应明确组合</text>
  <text x="800" y="810" class="param-text">• 易于实现: 简单的百分位数计算+二维判断</text>
</svg>
