<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="700" xmlns="http://www.w3.org/2000/svg">
  <style>
    .title { font-family: 'Microsoft YaHei', sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; fill: #333; }
    .step-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; fill: #333; }
    .decision-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 10px; font-weight: bold; text-anchor: middle; fill: #333; }
    .param-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 10px; text-anchor: start; fill: #555; }
    .param-text-center { font-family: 'Microsoft YaHei', sans-serif; font-size: 10px; text-anchor: middle; fill: #555; }
    .branch-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 9px; text-anchor: middle; fill: #666; }
    .grade-text { font-family: 'Microsoft YaHei', sans-serif; font-size: 11px; font-weight: bold; text-anchor: middle; fill: #333; }
    .connection { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    .grade-a { fill: #c8e6c9; stroke: #2e7d32; stroke-width: 2; }
    .grade-b { fill: #fff9c4; stroke: #f57f17; stroke-width: 2; }
    .grade-c { fill: #ffcdd2; stroke: #d32f2f; stroke-width: 2; }
  </style>
  
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333"/>
    </marker>
  </defs>

  <!-- 标题 -->
  <text x="800" y="30" class="title">图7 九档智能分组决策流程图</text>
  
  <!-- 输入参数 -->
  <rect x="700" y="70" width="200" height="50" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="800" y="95" class="step-text">输入参数</text>
  <text x="800" y="110" class="param-text-center">Rs, Rct阻抗值</text>
  
  <!-- 连接线：输入到联合判断 -->
  <line x1="800" y1="120" x2="800" y2="150" class="connection"/>
  
  <!-- Rs×Rct联合分档判断 -->
  <polygon points="800,150 920,180 800,210 680,180" fill="#fff9c4" stroke="#333" stroke-width="2"/>
  <text x="800" y="175" class="decision-text">Rs×Rct</text>
  <text x="800" y="190" class="decision-text">联合分档</text>
  
  <!-- 连接线：联合判断到九个等级 -->
  <line x1="720" y1="180" x2="200" y2="280" class="connection"/>
  <line x1="740" y1="185" x2="340" y2="280" class="connection"/>
  <line x1="760" y1="190" x2="480" y2="280" class="connection"/>
  
  <line x1="780" y1="195" x2="620" y2="280" class="connection"/>
  <line x1="800" y1="210" x2="760" y2="280" class="connection"/>
  <line x1="820" y1="195" x2="900" y2="280" class="connection"/>
  
  <line x1="840" y1="190" x2="1040" y2="280" class="connection"/>
  <line x1="860" y1="185" x2="1180" y2="280" class="connection"/>
  <line x1="880" y1="180" x2="1320" y2="280" class="connection"/>
  
  <!-- 九个等级输出框（3×3矩阵布局） -->
  <!-- Rs低档组合 -->
  <rect x="140" y="280" width="120" height="60" class="grade-a" rx="5"/>
  <text x="200" y="305" class="grade-text">1-1级</text>
  <text x="200" y="320" class="param-text-center">Rs低+Rct低</text>
  <text x="200" y="335" class="param-text-center">优秀电池</text>
  
  <rect x="280" y="280" width="120" height="60" class="grade-a" rx="5"/>
  <text x="340" y="305" class="grade-text">1-2级</text>
  <text x="340" y="320" class="param-text-center">Rs低+Rct中</text>
  <text x="340" y="335" class="param-text-center">良好电池</text>

  <rect x="420" y="280" width="120" height="60" class="grade-b" rx="5"/>
  <text x="480" y="305" class="grade-text">1-3级</text>
  <text x="480" y="320" class="param-text-center">Rs低+Rct高</text>
  <text x="480" y="335" class="param-text-center">一般电池</text>

  <!-- Rs中档组合 -->
  <rect x="560" y="280" width="120" height="60" class="grade-a" rx="5"/>
  <text x="620" y="305" class="grade-text">2-1级</text>
  <text x="620" y="320" class="param-text-center">Rs中+Rct低</text>
  <text x="620" y="335" class="param-text-center">良好电池</text>

  <rect x="700" y="280" width="120" height="60" class="grade-b" rx="5"/>
  <text x="760" y="305" class="grade-text">2-2级</text>
  <text x="760" y="320" class="param-text-center">Rs中+Rct中</text>
  <text x="760" y="335" class="param-text-center">一般电池</text>

  <rect x="840" y="280" width="120" height="60" class="grade-b" rx="5"/>
  <text x="900" y="305" class="grade-text">2-3级</text>
  <text x="900" y="320" class="param-text-center">Rs中+Rct高</text>
  <text x="900" y="335" class="param-text-center">较差电池</text>
  
  <!-- Rs高档组合 -->
  <rect x="980" y="280" width="120" height="60" class="grade-b" rx="5"/>
  <text x="1040" y="305" class="grade-text">3-1级</text>
  <text x="1040" y="320" class="param-text-center">Rs高+Rct低</text>
  <text x="1040" y="335" class="param-text-center">较差电池</text>

  <rect x="1120" y="280" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1180" y="305" class="grade-text">3-2级</text>
  <text x="1180" y="320" class="param-text-center">Rs高+Rct中</text>
  <text x="1180" y="335" class="param-text-center">劣质电池</text>

  <rect x="1260" y="280" width="120" height="60" class="grade-c" rx="5"/>
  <text x="1320" y="305" class="grade-text">3-3级</text>
  <text x="1320" y="320" class="param-text-center">Rs高+Rct高</text>
  <text x="1320" y="335" class="param-text-center">废品电池</text>
  
  <!-- 分支标签文字 -->
  <text x="200" y="265" class="branch-text">Rs低+Rct低</text>
  <text x="340" y="265" class="branch-text">Rs低+Rct中</text>
  <text x="480" y="265" class="branch-text">Rs低+Rct高</text>
  
  <text x="620" y="265" class="branch-text">Rs中+Rct低</text>
  <text x="760" y="265" class="branch-text">Rs中+Rct中</text>
  <text x="900" y="265" class="branch-text">Rs中+Rct高</text>
  
  <text x="1040" y="265" class="branch-text">Rs高+Rct低</text>
  <text x="1180" y="265" class="branch-text">Rs高+Rct中</text>
  <text x="1320" y="265" class="branch-text">Rs高+Rct高</text>

  <!-- 分组策略说明框 -->
  <rect x="80" y="380" width="1440" height="180" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="800" y="405" class="step-text">九档智能分组策略 (自适应分位数分档)</text>

  <!-- 分位数概念说明 -->
  <text x="100" y="430" class="param-text">分位数分档原理:</text>
  <text x="100" y="445" class="param-text">• P33 (33%分位数): 将样本从小到大排序，33%位置的值</text>
  <text x="100" y="460" class="param-text">• P67 (67%分位数): 将样本从小到大排序，67%位置的值</text>
  <text x="100" y="475" class="param-text">• 自动分档: Rs低档(≤P33)、Rs中档(P33-P67)、Rs高档(>P67)</text>
  <text x="100" y="490" class="param-text">• 同理: Rct低档(≤P33)、Rct中档(P33-P67)、Rct高档(>P67)</text>

  <!-- 具体示例说明 -->
  <text x="800" y="430" class="param-text">具体示例 (某款电池: Rs范围0.15-0.25mΩ, Rct范围0.01-0.1mΩ):</text>
  <text x="800" y="445" class="param-text">• Rs分档: 低档≤0.18mΩ, 中档0.18-0.22mΩ, 高档>0.22mΩ</text>
  <text x="800" y="460" class="param-text">• Rct分档: 低档≤0.04mΩ, 中档0.04-0.07mΩ, 高档>0.07mΩ</text>
  <text x="800" y="475" class="param-text">• 组合结果: 3×3=9个档位，如1-1级(Rs≤0.18+Rct≤0.04)</text>
  <text x="800" y="490" class="param-text">• 适应性: 不同电池自动计算不同的分档阈值</text>

  <!-- 技术优势 -->
  <text x="100" y="520" class="param-text">技术优势:</text>
  <text x="100" y="535" class="param-text">• 通用性: 适用于任何电池品种，无需预设阈值</text>
  <text x="100" y="550" class="param-text">• 精确性: 使用Rct(电荷转移阻抗)比Rp(极化阻抗)更准确</text>

  <text x="800" y="520" class="param-text">应用效果:</text>
  <text x="800" y="535" class="param-text">• 分组精度: >95%, 一致性提升: >30%</text>
  <text x="800" y="550" class="param-text">• 实现简单: 分位数计算+二维判断逻辑</text>

  <!-- 分组结果统计框 -->
  <rect x="200" y="580" width="1200" height="80" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="800" y="605" class="step-text">自适应九档分组的创新点</text>
  <text x="220" y="630" class="param-text">• 解决固定阈值问题: 传统方法需要针对每种电池设定不同阈值</text>
  <text x="220" y="645" class="param-text">• 提高分组精度: 使用Rct替代Rp，更准确反映电化学性能</text>

  <text x="800" y="630" class="param-text">• 简化操作流程: 一步完成Rs×Rct联合分档</text>
  <text x="800" y="645" class="param-text">• 工业化应用: 算法简单，易于在生产线上实现</text>
</svg>
