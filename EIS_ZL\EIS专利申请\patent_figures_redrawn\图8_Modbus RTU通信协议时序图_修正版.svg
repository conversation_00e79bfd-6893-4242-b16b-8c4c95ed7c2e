<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .device-label { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; }
      .time-label { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .data-label { font-family: Arial, sans-serif; font-size: 9px; text-anchor: middle; }
      .field-label { font-family: Arial, sans-serif; font-size: 8px; text-anchor: middle; fill: #666; }
      .signal-high { stroke: #2e7d32; stroke-width: 3; fill: none; }
      .signal-low { stroke: #d32f2f; stroke-width: 3; fill: none; }
      .signal-idle { stroke: #666; stroke-width: 2; fill: none; stroke-dasharray: 5,5; }
      .frame-box { stroke: #1976d2; stroke-width: 2; fill: rgba(25, 118, 210, 0.1); }
      .response-box { stroke: #388e3c; stroke-width: 2; fill: rgba(56, 142, 60, 0.1); }
    </style>
    <marker id="arrow" markerWidth="10" markerHeight="10" refX="9" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L9,3 z" fill="#333"/>
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">图8 Modbus RTU通信协议时序图</text>
  
  <!-- 设备标签 -->
  <text x="50" y="80" class="device-label">主站(PC)</text>
  <text x="50" y="200" class="device-label">从站(设备)</text>
  
  <!-- 时间轴 -->
  <line x1="150" y1="50" x2="1150" y2="50" stroke="#333" stroke-width="1"/>
  <text x="650" y="40" class="time-label">时间轴 (ms)</text>
  
  <!-- 时间刻度 -->
  <g class="time-label">
    <line x1="200" y1="45" x2="200" y2="55"/>
    <text x="200" y="70">0</text>
    
    <line x1="350" y1="45" x2="350" y2="55"/>
    <text x="350" y="70">10</text>
    
    <line x1="500" y1="45" x2="500" y2="55"/>
    <text x="500" y="70">20</text>
    
    <line x1="650" y1="45" x2="650" y2="55"/>
    <text x="650" y="70">30</text>
    
    <line x1="800" y1="45" x2="800" y2="55"/>
    <text x="800" y="70">40</text>
    
    <line x1="950" y1="45" x2="950" y2="55"/>
    <text x="950" y="70">50</text>
    
    <line x1="1100" y1="45" x2="1100" y2="55"/>
    <text x="1100" y="70">60</text>
  </g>
  
  <!-- 主站信号线 -->
  <line x1="150" y1="100" x2="1150" y2="100" stroke="#333" stroke-width="1"/>
  
  <!-- 从站信号线 -->
  <line x1="150" y1="220" x2="1150" y2="220" stroke="#333" stroke-width="1"/>
  
  <!-- 空闲状态 -->
  <line x1="150" y1="100" x2="200" y2="100" class="signal-idle"/>
  <line x1="150" y1="220" x2="200" y2="220" class="signal-idle"/>
  <text x="175" y="95" class="data-label">空闲</text>
  <text x="175" y="235" class="data-label">空闲</text>
  
  <!-- 主站发送请求帧 -->
  <rect x="200" y="85" width="200" height="30" class="frame-box" rx="3"/>
  <text x="300" y="105" class="data-label">请求帧</text>
  
  <!-- 请求帧详细结构 -->
  <rect x="200" y="120" width="30" height="20" fill="#ffcdd2" stroke="#333"/>
  <text x="215" y="135" class="field-label">地址</text>
  <text x="215" y="150" class="field-label">01</text>
  
  <rect x="230" y="120" width="40" height="20" fill="#fff9c4" stroke="#333"/>
  <text x="250" y="135" class="field-label">功能码</text>
  <text x="250" y="150" class="field-label">03</text>
  
  <rect x="270" y="120" width="60" height="20" fill="#e1f5fe" stroke="#333"/>
  <text x="300" y="135" class="field-label">起始地址</text>
  <text x="300" y="150" class="field-label">0000</text>
  
  <rect x="330" y="120" width="40" height="20" fill="#e8f5e8" stroke="#333"/>
  <text x="350" y="135" class="field-label">数量</text>
  <text x="350" y="150" class="field-label">0A</text>
  
  <rect x="370" y="120" width="30" height="20" fill="#f3e5f5" stroke="#333"/>
  <text x="385" y="135" class="field-label">CRC</text>
  <text x="385" y="150" class="field-label">C5CD</text>
  
  <!-- 传输延时 -->
  <line x1="400" y1="100" x2="450" y2="100" class="signal-idle"/>
  <text x="425" y="95" class="data-label">传输</text>
  
  <!-- 从站接收处理时间 -->
  <rect x="450" y="205" width="100" height="30" fill="#fffde7" stroke="#f57f17" stroke-width="1" rx="3"/>
  <text x="500" y="225" class="data-label">处理时间</text>
  
  <!-- 从站发送响应帧 -->
  <rect x="600" y="205" width="250" height="30" class="response-box" rx="3"/>
  <text x="725" y="225" class="data-label">响应帧</text>
  
  <!-- 响应帧详细结构 -->
  <rect x="600" y="250" width="30" height="20" fill="#ffcdd2" stroke="#333"/>
  <text x="615" y="265" class="field-label">地址</text>
  <text x="615" y="280" class="field-label">01</text>
  
  <rect x="630" y="250" width="40" height="20" fill="#fff9c4" stroke="#333"/>
  <text x="650" y="265" class="field-label">功能码</text>
  <text x="650" y="280" class="field-label">03</text>
  
  <rect x="670" y="250" width="40" height="20" fill="#e1f5fe" stroke="#333"/>
  <text x="690" y="265" class="field-label">字节数</text>
  <text x="690" y="280" class="field-label">14</text>
  
  <rect x="710" y="250" width="100" height="20" fill="#e8f5e8" stroke="#333"/>
  <text x="760" y="265" class="field-label">数据</text>
  <text x="760" y="280" class="field-label">测量数据(20字节)</text>
  
  <rect x="810" y="250" width="40" height="20" fill="#f3e5f5" stroke="#333"/>
  <text x="830" y="265" class="field-label">CRC</text>
  <text x="830" y="280" class="field-label">4A2B</text>
  
  <!-- 主站接收确认 -->
  <rect x="900" y="85" width="80" height="30" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="940" y="105" class="data-label">接收确认</text>
  
  <!-- 通信完成，返回空闲 -->
  <line x1="1000" y1="100" x2="1150" y2="100" class="signal-idle"/>
  <line x1="900" y1="220" x2="1150" y2="220" class="signal-idle"/>
  <text x="1075" y="95" class="data-label">空闲</text>
  <text x="1025" y="235" class="data-label">空闲</text>
  
  <!-- 请求传输箭头 -->
  <line x1="300" y1="115" x2="500" y2="205" stroke="#1976d2" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="400" y="165" class="data-label" transform="rotate(-25, 400, 165)">请求传输</text>
  
  <!-- 响应传输箭头 -->
  <line x1="725" y1="205" x2="940" y2="115" stroke="#388e3c" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="830" y="165" class="data-label" transform="rotate(25, 830, 165)">响应传输</text>
  
  <!-- 协议参数说明 -->
  <rect x="100" y="320" width="1000" height="180" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="600" y="345" class="device-label">Modbus RTU协议参数</text>
  
  <!-- 通信参数 -->
  <text x="120" y="370" class="data-label">通信参数:</text>
  <text x="120" y="385" class="field-label">• 波特率: 115200 bps</text>
  <text x="120" y="400" class="field-label">• 数据位: 8位</text>
  <text x="120" y="415" class="field-label">• 停止位: 1位</text>
  <text x="120" y="430" class="field-label">• 校验位: 无</text>
  
  <!-- 帧格式 -->
  <text x="350" y="370" class="data-label">帧格式:</text>
  <text x="350" y="385" class="field-label">• 地址域: 1字节 (从站地址)</text>
  <text x="350" y="400" class="field-label">• 功能码: 1字节 (操作类型)</text>
  <text x="350" y="415" class="field-label">• 数据域: 0-252字节</text>
  <text x="350" y="430" class="field-label">• CRC校验: 2字节</text>
  
  <!-- 功能码说明 -->
  <text x="600" y="370" class="data-label">常用功能码:</text>
  <text x="600" y="385" class="field-label">• 03H: 读保持寄存器</text>
  <text x="600" y="400" class="field-label">• 04H: 读输入寄存器</text>
  <text x="600" y="415" class="field-label">• 06H: 写单个寄存器</text>
  <text x="600" y="430" class="field-label">• 10H: 写多个寄存器</text>
  
  <!-- 时序特性 -->
  <text x="850" y="370" class="data-label">时序特性:</text>
  <text x="850" y="385" class="field-label">• 字符间隔: &lt;1.5字符时间</text>
  <text x="850" y="400" class="field-label">• 帧间隔: ≥3.5字符时间</text>
  <text x="850" y="415" class="field-label">• 响应超时: 1000ms</text>
  <text x="850" y="430" class="field-label">• 重传次数: 3次</text>
  
  <!-- 数据寄存器映射 -->
  <text x="120" y="460" class="data-label">寄存器映射:</text>
  <text x="120" y="475" class="field-label">• 40001-40010: Rs测量值 (mΩ)</text>
  <text x="120" y="490" class="field-label">• 40011-40020: Rp测量值 (mΩ)</text>
  
  <text x="400" y="460" class="field-label">• 40021-40030: 频率设置 (Hz)</text>
  <text x="400" y="475" class="field-label">• 40031-40040: 电流设置 (mA)</text>
  <text x="400" y="490" class="field-label">• 40041-40050: 状态寄存器</text>
  
  <text x="700" y="460" class="field-label">• 40051-40060: 分组结果</text>
  <text x="700" y="475" class="field-label">• 40061-40070: 错误代码</text>
  <text x="700" y="490" class="field-label">• 40071-40080: 系统参数</text>
</svg>
