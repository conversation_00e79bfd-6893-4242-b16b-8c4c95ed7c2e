<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .device-label { font-family: "Microsoft YaHei", Arial; font-size: 14px; font-weight: bold; text-anchor: start; }
      .time-label { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .data-label { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; }
      .field-label { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: middle; fill: #666; }
      .param-text { font-family: "Microsoft YaHei", Arial; font-size: 8px; text-anchor: start; fill: #333; }
      .signal-idle { stroke: #999; stroke-width: 2; fill: none; stroke-dasharray: 3,3; }
      .signal-data { stroke: #1976d2; stroke-width: 3; fill: none; }
      .signal-response { stroke: #388e3c; stroke-width: 3; fill: none; }
      .signal-error { stroke: #d32f2f; stroke-width: 3; fill: none; }
      .frame-box { stroke: #1976d2; stroke-width: 2; fill: rgba(25, 118, 210, 0.1); }
      .response-box { stroke: #388e3c; stroke-width: 2; fill: rgba(56, 142, 60, 0.1); }
      .error-box { stroke: #d32f2f; stroke-width: 2; fill: rgba(211, 47, 47, 0.1); }
    </style>
    <marker id="arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" class="title">图8 Modbus RTU通信协议时序图</text>
  
  <!-- 设备标签 -->
  <text x="80" y="85" class="device-label">主站(PC上位机)</text>
  <text x="80" y="160" class="device-label">从站(DNB1101BB设备)</text>
  
  <!-- 时间轴 -->
  <line x1="200" y1="50" x2="1150" y2="50" stroke="#333" stroke-width="1"/>
  <text x="675" y="40" class="time-label">时间轴 (ms)</text>
  
  <!-- 时间刻度 -->
  <g class="time-label">
    <line x1="250" y1="45" x2="250" y2="55"/>
    <text x="250" y="70">0</text>
    
    <line x1="350" y1="45" x2="350" y2="55"/>
    <text x="350" y="70">10</text>
    
    <line x1="450" y1="45" x2="450" y2="55"/>
    <text x="450" y="70">20</text>
    
    <line x1="550" y1="45" x2="550" y2="55"/>
    <text x="550" y="70">30</text>
    
    <line x1="650" y1="45" x2="650" y2="55"/>
    <text x="650" y="70">40</text>
    
    <line x1="750" y1="45" x2="750" y2="55"/>
    <text x="750" y="70">50</text>
    
    <line x1="850" y1="45" x2="850" y2="55"/>
    <text x="850" y="70">60</text>
    
    <line x1="950" y1="45" x2="950" y2="55"/>
    <text x="950" y="70">70</text>
    
    <line x1="1050" y1="45" x2="1050" y2="55"/>
    <text x="1050" y="70">80</text>
  </g>
  
  <!-- 信号线 -->
  <line x1="200" y1="100" x2="1150" y2="100" stroke="#333" stroke-width="1"/>
  <line x1="200" y1="175" x2="1150" y2="175" stroke="#333" stroke-width="1"/>
  
  <!-- 序列1: 正常读取阻抗数据 -->
  <!-- 空闲状态 -->
  <line x1="200" y1="100" x2="250" y2="100" class="signal-idle"/>
  <line x1="200" y1="175" x2="250" y2="175" class="signal-idle"/>
  <text x="225" y="95" class="data-label">空闲</text>
  <text x="225" y="170" class="data-label">空闲</text>
  
  <!-- 主站发送请求帧 -->
  <rect x="250" y="85" width="100" height="30" class="frame-box" rx="3"/>
  <text x="300" y="105" class="data-label">读阻抗数据请求</text>
  
  <!-- 请求帧详细结构 -->
  <rect x="250" y="120" width="15" height="15" fill="#ffcdd2" stroke="#333"/>
  <text x="257" y="132" class="field-label">01</text>
  
  <rect x="265" y="120" width="15" height="15" fill="#fff9c4" stroke="#333"/>
  <text x="272" y="132" class="field-label">03</text>
  
  <rect x="280" y="120" width="25" height="15" fill="#e1f5fe" stroke="#333"/>
  <text x="292" y="132" class="field-label">3000</text>
  
  <rect x="305" y="120" width="20" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="315" y="132" class="field-label">10</text>
  
  <rect x="325" y="120" width="25" height="15" fill="#f3e5f5" stroke="#333"/>
  <text x="337" y="132" class="field-label">CRC</text>
  
  <!-- 传输延时 -->
  <line x1="350" y1="100" x2="380" y2="100" class="signal-idle"/>
  <text x="365" y="95" class="data-label">传输</text>
  
  <!-- 从站接收处理 -->
  <rect x="380" y="160" width="70" height="30" fill="#fffde7" stroke="#f57f17" stroke-width="1" rx="3"/>
  <text x="415" y="180" class="data-label">数据处理</text>
  
  <!-- 从站发送响应帧 -->
  <rect x="480" y="160" width="120" height="30" class="response-box" rx="3"/>
  <text x="540" y="180" class="data-label">阻抗数据响应</text>
  
  <!-- 响应帧详细结构 -->
  <rect x="480" y="195" width="15" height="15" fill="#ffcdd2" stroke="#333"/>
  <text x="487" y="207" class="field-label">01</text>
  
  <rect x="495" y="195" width="15" height="15" fill="#fff9c4" stroke="#333"/>
  <text x="502" y="207" class="field-label">03</text>
  
  <rect x="510" y="195" width="20" height="15" fill="#e1f5fe" stroke="#333"/>
  <text x="520" y="207" class="field-label">20</text>
  
  <rect x="530" y="195" width="45" height="15" fill="#e8f5e8" stroke="#333"/>
  <text x="552" y="207" class="field-label">阻抗数据</text>
  
  <rect x="575" y="195" width="25" height="15" fill="#f3e5f5" stroke="#333"/>
  <text x="587" y="207" class="field-label">CRC</text>
  
  <!-- 主站接收确认 -->
  <rect x="630" y="85" width="70" height="30" fill="#c8e6c9" stroke="#388e3c" stroke-width="1" rx="3"/>
  <text x="665" y="105" class="data-label">接收确认</text>
  
  <!-- 传输箭头 -->
  <line x1="300" y1="115" x2="415" y2="160" stroke="#1976d2" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="350" y="140" class="data-label">请求传输</text>
  
  <line x1="540" y1="160" x2="665" y2="115" stroke="#388e3c" stroke-width="2" marker-end="url(#arrow)"/>
  <text x="590" y="140" class="data-label">响应传输</text>
  
  <!-- 序列2: 写入频率设置 -->
  <!-- 第二次通信 -->
  <rect x="750" y="85" width="100" height="30" class="frame-box" rx="3"/>
  <text x="800" y="105" class="data-label">写频率设置</text>
  
  <rect x="880" y="160" width="100" height="30" class="response-box" rx="3"/>
  <text x="930" y="180" class="data-label">设置确认</text>
  
  <!-- 传输箭头 -->
  <line x1="800" y1="115" x2="930" y2="160" stroke="#1976d2" stroke-width="2" marker-end="url(#arrow)"/>
  <line x1="930" y1="160" x2="800" y2="115" stroke="#388e3c" stroke-width="2" marker-end="url(#arrow)"/>
  
  <!-- 返回空闲 -->
  <line x1="1000" y1="100" x2="1150" y2="100" class="signal-idle"/>
  <line x1="1000" y1="175" x2="1150" y2="175" class="signal-idle"/>
  <text x="1075" y="95" class="data-label">空闲</text>
  <text x="1075" y="170" class="data-label">空闲</text>
  
  <!-- 协议参数说明框 - 严格控制边界 -->
  <rect x="80" y="280" width="1040" height="180" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="600" y="305" class="device-label">Modbus RTU协议参数</text>
  
  <!-- 左列参数 -->
  <text x="100" y="330" class="param-text">通信参数:</text>
  <text x="100" y="345" class="param-text">• 波特率: 115200 bps</text>
  <text x="100" y="360" class="param-text">• 数据位: 8位</text>
  <text x="100" y="375" class="param-text">• 停止位: 1位</text>
  <text x="100" y="390" class="param-text">• 校验位: 无</text>
  <text x="100" y="405" class="param-text">• 设备地址: 1-247</text>
  <text x="100" y="420" class="param-text">• 响应超时: 1000ms</text>
  <text x="100" y="435" class="param-text">• 重传次数: 3次</text>
  
  <!-- 中列参数 -->
  <text x="350" y="330" class="param-text">帧格式:</text>
  <text x="350" y="345" class="param-text">• 地址域: 1字节</text>
  <text x="350" y="360" class="param-text">• 功能码: 1字节</text>
  <text x="350" y="375" class="param-text">• 数据域: 0-252字节</text>
  <text x="350" y="390" class="param-text">• CRC校验: 2字节</text>
  <text x="350" y="405" class="param-text">• 帧间隔: ≥3.5字符时间</text>
  <text x="350" y="420" class="param-text">• 字符间隔: <1.5字符时间</text>
  
  <!-- 右列参数 -->
  <text x="600" y="330" class="param-text">功能码:</text>
  <text x="600" y="345" class="param-text">• 03H: 读保持寄存器</text>
  <text x="600" y="360" class="param-text">• 04H: 读输入寄存器</text>
  <text x="600" y="375" class="param-text">• 06H: 写单个寄存器</text>
  <text x="600" y="390" class="param-text">• 10H: 写多个寄存器</text>
  <text x="600" y="405" class="param-text">• 异常响应: 功能码+80H</text>
  
  <!-- 最右列参数 -->
  <text x="850" y="330" class="param-text">寄存器地址:</text>
  <text x="850" y="345" class="param-text">• 3000H-30FFH: 阻抗数据</text>
  <text x="850" y="360" class="param-text">• 4200H-427FH: 频率设置</text>
  <text x="850" y="375" class="param-text">• 4280H-42BFH: 增益设置</text>
  <text x="850" y="390" class="param-text">• 40C0H-40FFH: 采样电阻</text>
  <text x="850" y="405" class="param-text">• 3380H-33BFH: 状态寄存器</text>
</svg>
