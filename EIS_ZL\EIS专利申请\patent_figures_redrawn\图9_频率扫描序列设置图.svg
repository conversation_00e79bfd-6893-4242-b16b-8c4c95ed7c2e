<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: "Microsoft YaHei", Arial; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .axis-label { font-family: "Microsoft YaHei", Arial; font-size: 14px; text-anchor: middle; }
      .tick-label { font-family: "Microsoft YaHei", Arial; font-size: 10px; text-anchor: middle; }
      .freq-label { font-family: "Microsoft YaHei", Arial; font-size: 9px; text-anchor: middle; }
      .param-label { font-family: "Microsoft YaHei", Arial; font-size: 10px; }
      .section-title { font-family: "Microsoft YaHei", Arial; font-size: 12px; font-weight: bold; }
      .grid-line { stroke: #e0e0e0; stroke-width: 0.5; }
      .axis-line { stroke: #333; stroke-width: 2; }
      .freq-point { stroke: #1976d2; stroke-width: 2; fill: #1976d2; }
      .freq-line { stroke: #1976d2; stroke-width: 1; stroke-dasharray: 2,2; }
      .region-high { fill: rgba(244, 67, 54, 0.2); stroke: #f44336; stroke-width: 1; }
      .region-mid { fill: rgba(255, 152, 0, 0.2); stroke: #ff9800; stroke-width: 1; }
      .region-low { fill: rgba(76, 175, 80, 0.2); stroke: #4caf50; stroke-width: 1; }
    </style>
    <marker id="arrowhead" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#333" />
    </marker>
  </defs>
  
  <!-- 标题 -->
  <text x="500" y="30" class="title">图9 频率扫描序列设置图</text>
  
  <!-- 主坐标系 -->
  <!-- X轴 (对数频率轴) -->
  <line x1="100" y1="400" x2="850" y2="400" class="axis-line" marker-end="url(#arrowhead)"/>
  <text x="475" y="440" class="axis-label">频率 (Hz) - 对数刻度</text>
  
  <!-- Y轴 (扫描点密度) -->
  <line x1="100" y1="400" x2="100" y2="100" class="axis-line" marker-end="url(#arrowhead)"/>
  <text x="50" y="250" class="axis-label" transform="rotate(-90, 50, 250)">扫描点密度</text>
  
  <!-- 频率刻度 (对数刻度) -->
  <g class="grid-line">
    <!-- 0.1 Hz -->
    <line x1="150" y1="395" x2="150" y2="405"/>
    <text x="150" y="420" class="tick-label">0.1</text>
    <line x1="150" y1="400" x2="150" y2="100"/>
    
    <!-- 1 Hz -->
    <line x1="250" y1="395" x2="250" y2="405"/>
    <text x="250" y="420" class="tick-label">1</text>
    <line x1="250" y1="400" x2="250" y2="100"/>
    
    <!-- 10 Hz -->
    <line x1="350" y1="395" x2="350" y2="405"/>
    <text x="350" y="420" class="tick-label">10</text>
    <line x1="350" y1="400" x2="350" y2="100"/>
    
    <!-- 100 Hz -->
    <line x1="450" y1="395" x2="450" y2="405"/>
    <text x="450" y="420" class="tick-label">100</text>
    <line x1="450" y1="400" x2="450" y2="100"/>
    
    <!-- 1 kHz -->
    <line x1="550" y1="395" x2="550" y2="405"/>
    <text x="550" y="420" class="tick-label">1k</text>
    <line x1="550" y1="400" x2="550" y2="100"/>
    
    <!-- 10 kHz -->
    <line x1="650" y1="395" x2="650" y2="405"/>
    <text x="650" y="420" class="tick-label">10k</text>
    <line x1="650" y1="400" x2="650" y2="100"/>
    
    <!-- 100 kHz -->
    <line x1="750" y1="395" x2="750" y2="405"/>
    <text x="750" y="420" class="tick-label">100k</text>
    <line x1="750" y1="400" x2="750" y2="100"/>
  </g>
  
  <!-- Y轴刻度 -->
  <g class="grid-line">
    <line x1="95" y1="350" x2="105" y2="350"/>
    <text x="85" y="355" class="tick-label">低</text>
    <line x1="100" y1="350" x2="850" y2="350"/>
    
    <line x1="95" y1="300" x2="105" y2="300"/>
    <text x="85" y="305" class="tick-label">中</text>
    <line x1="100" y1="300" x2="850" y2="300"/>
    
    <line x1="95" y1="250" x2="105" y2="250"/>
    <text x="85" y="255" class="tick-label">高</text>
    <line x1="100" y1="250" x2="850" y2="250"/>
    
    <line x1="95" y1="200" x2="105" y2="200"/>
    <text x="85" y="205" class="tick-label">很高</text>
    <line x1="100" y1="200" x2="850" y2="200"/>
    
    <line x1="95" y1="150" x2="105" y2="150"/>
    <text x="85" y="155" class="tick-label">极高</text>
    <line x1="100" y1="150" x2="850" y2="150"/>
  </g>
  
  <!-- 频率区域划分 -->
  <!-- 低频区域 -->
  <rect x="150" y="100" width="200" height="300" class="region-low"/>
  <text x="250" y="120" class="section-title">低频区域</text>
  <text x="250" y="135" class="param-label">0.1 - 10 Hz</text>
  
  <!-- 中频区域 -->
  <rect x="350" y="100" width="200" height="300" class="region-mid"/>
  <text x="450" y="120" class="section-title">中频区域</text>
  <text x="450" y="135" class="param-label">10 - 1000 Hz</text>
  
  <!-- 高频区域 -->
  <rect x="550" y="100" width="200" height="300" class="region-high"/>
  <text x="650" y="120" class="section-title">高频区域</text>
  <text x="650" y="135" class="param-label">1k - 100k Hz</text>
  
  <!-- 扫描点分布曲线 -->
  <path d="M 150 350 Q 200 320 250 280 Q 300 250 350 230 Q 400 200 450 180 Q 500 170 550 160 Q 600 155 650 150 Q 700 148 750 150" 
        stroke="#1976d2" stroke-width="3" fill="none"/>
  
  <!-- 关键频率点标注 -->
  <!-- 低频关键点 -->
  <circle cx="150" cy="350" r="4" class="freq-point"/>
  <text x="150" y="370" class="freq-label">0.1Hz</text>
  <text x="150" y="385" class="freq-label">起始点</text>
  
  <circle cx="200" cy="320" r="4" class="freq-point"/>
  <text x="200" y="340" class="freq-label">0.5Hz</text>
  
  <circle cx="250" cy="280" r="4" class="freq-point"/>
  <text x="250" y="300" class="freq-label">1Hz</text>
  
  <circle cx="300" cy="250" r="4" class="freq-point"/>
  <text x="300" y="270" class="freq-label">5Hz</text>
  
  <!-- 中频关键点 -->
  <circle cx="350" cy="230" r="4" class="freq-point"/>
  <text x="350" y="210" class="freq-label">10Hz</text>
  
  <circle cx="400" cy="200" r="4" class="freq-point"/>
  <text x="400" y="180" class="freq-label">50Hz</text>
  
  <circle cx="450" cy="180" r="4" class="freq-point"/>
  <text x="450" y="160" class="freq-label">100Hz</text>
  
  <circle cx="500" cy="170" r="4" class="freq-point"/>
  <text x="500" y="150" class="freq-label">500Hz</text>
  
  <!-- 高频关键点 -->
  <circle cx="550" cy="160" r="4" class="freq-point"/>
  <text x="550" y="140" class="freq-label">1kHz</text>
  
  <circle cx="600" cy="155" r="4" class="freq-point"/>
  <text x="600" y="135" class="freq-label">5kHz</text>
  
  <circle cx="650" cy="150" r="4" class="freq-point"/>
  <text x="650" y="130" class="freq-label">10kHz</text>
  
  <circle cx="750" cy="150" r="4" class="freq-point"/>
  <text x="750" y="370" class="freq-label">100kHz</text>
  <text x="750" y="385" class="freq-label">终止点</text>
  
  <!-- 扫描策略说明 -->
  <rect x="50" y="480" width="900" height="280" fill="#f5f5f5" stroke="#666" stroke-width="1" rx="5"/>
  <text x="500" y="505" class="section-title">频率扫描策略</text>
  
  <!-- 三个区域的扫描策略 -->
  <rect x="70" y="520" width="280" height="120" fill="#e8f5e8" stroke="#4caf50" stroke-width="1" rx="3"/>
  <text x="210" y="540" class="section-title">低频区域 (0.1-10Hz)</text>
  <text x="80" y="560" class="param-label">• 扫描点数: 20点</text>
  <text x="80" y="575" class="param-label">• 分布策略: 对数均匀分布</text>
  <text x="80" y="590" class="param-label">• 测量时间: 每点5秒</text>
  <text x="80" y="605" class="param-label">• 主要目的: Rp参数提取</text>
  <text x="80" y="620" class="param-label">• 特点: 高精度，长时间</text>
  
  <rect x="360" y="520" width="280" height="120" fill="#fff3e0" stroke="#ff9800" stroke-width="1" rx="3"/>
  <text x="500" y="540" class="section-title">中频区域 (10-1000Hz)</text>
  <text x="370" y="560" class="param-label">• 扫描点数: 30点</text>
  <text x="370" y="575" class="param-label">• 分布策略: 对数密集分布</text>
  <text x="370" y="590" class="param-label">• 测量时间: 每点2秒</text>
  <text x="370" y="605" class="param-label">• 主要目的: 过渡区域分析</text>
  <text x="370" y="620" class="param-label">• 特点: 平衡精度与速度</text>
  
  <rect x="650" y="520" width="280" height="120" fill="#ffebee" stroke="#f44336" stroke-width="1" rx="3"/>
  <text x="790" y="540" class="section-title">高频区域 (1k-100kHz)</text>
  <text x="660" y="560" class="param-label">• 扫描点数: 15点</text>
  <text x="660" y="575" class="param-label">• 分布策略: 对数稀疏分布</text>
  <text x="660" y="590" class="param-label">• 测量时间: 每点0.5秒</text>
  <text x="660" y="605" class="param-label">• 主要目的: Rs参数提取</text>
  <text x="660" y="620" class="param-label">• 特点: 快速扫描</text>
  
  <!-- 自适应扫描策略 -->
  <rect x="70" y="660" width="860" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="1" rx="3"/>
  <text x="500" y="680" class="section-title">自适应扫描策略</text>
  <text x="80" y="700" class="param-label">• 根据电池类型自动调整扫描点分布</text>
  <text x="80" y="715" class="param-label">• 实时监测信号质量，动态增减扫描点</text>
  <text x="80" y="730" class="param-label">• 总扫描时间: 约3-5分钟，总扫描点数: 65点</text>
  
  <text x="500" y="700" class="param-label">• 温度补偿: 根据环境温度调整频率范围</text>
  <text x="500" y="715" class="param-label">• 噪声抑制: 自动滤除干扰频率点</text>
  <text x="500" y="730" class="param-label">• 精度保证: 关键频率点重复测量</text>
</svg>
