图1_系统整体架构图 - 详细绘制指导

=== diagrams.net 绘制步骤 ===

1. 创建新图表
   - 文件名：图1_系统整体架构图
   - 类型：流程图

2. 模块布局（从上到下）：

第一层：
- 用户界面模块 [矩形，颜色：#e1f5fe]

第二层：
- 主控制器 [矩形，颜色：#f3e5f5]

第三层（并排）：
- DNB1101BB芯片模块 [矩形，颜色：#fff3e0]
- 外部电流源模块 [矩形，颜色：#fff3e0]  
- 通信接口模块 [矩形，颜色：#fff3e0]

第四层（对应上层）：
- 阻抗测量电路 [矩形]
- 电流控制电路 [矩形]
- Modbus RTU通信 [矩形]

第五层（汇聚）：
- 信号调理电路 [矩形]

第六层：
- ADC采样 [矩形]

第七层：
- 数据处理模块 [矩形，颜色：#e8f5e8]

第八层（并排）：
- 多维参数提取算法 [矩形]
- 九档智能分组决策 [矩形]
- 增益自适应调节 [矩形]

第九层：
- 测试结果输出 [矩形，颜色：#fce4ec]

第十层（并排）：
- 数据存储 [矩形]
- 结果显示 [矩形]
- 上位机通信 [矩形]

3. 连接关系：
- 用户界面模块 → 主控制器
- 主控制器 → DNB1101BB芯片模块
- 主控制器 → 外部电流源模块
- 主控制器 → 通信接口模块
- DNB1101BB芯片模块 → 阻抗测量电路
- 外部电流源模块 → 电流控制电路
- 通信接口模块 → Modbus RTU通信
- 阻抗测量电路 → 信号调理电路
- 电流控制电路 → 信号调理电路
- 信号调理电路 → ADC采样
- ADC采样 → 数据处理模块
- 数据处理模块 → 多维参数提取算法
- 数据处理模块 → 九档智能分组决策
- 数据处理模块 → 增益自适应调节
- 多维参数提取算法 → 测试结果输出
- 九档智能分组决策 → 测试结果输出
- 增益自适应调节 → 测试结果输出
- 测试结果输出 → 数据存储
- 测试结果输出 → 结果显示
- Modbus RTU通信 → 上位机通信

4. 格式设置：
- 字体：微软雅黑，12pt
- 边框：1pt，黑色
- 箭头：实心箭头
- 间距：模块间距离保持一致

=== LibreOffice Draw 绘制步骤 ===

1. 新建文档
   - 页面设置：A4横向
   - 标题：图1_系统整体架构图

2. 绘制工具：
   - 矩形工具：绘制模块
   - 连接器工具：绘制连接线
   - 文本工具：添加文字

3. 样式设置：
   - 形状填充：使用上述颜色代码
   - 线条样式：1pt实线
   - 字体：宋体，12pt

4. 保存格式：
   - 文件 → 另存为 → OpenDocument绘图(.odg)
   - 文件名：图1_系统整体架构图.odg

=== 注意事项 ===

1. 保持模块大小一致
2. 连接线要整齐，避免交叉
3. 文字居中对齐
4. 颜色搭配要协调
5. 整体布局要平衡美观
