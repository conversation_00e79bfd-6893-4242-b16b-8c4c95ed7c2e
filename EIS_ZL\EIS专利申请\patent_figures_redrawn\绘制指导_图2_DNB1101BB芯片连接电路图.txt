图2_DNB1101BB芯片连接电路图 - 详细绘制指导

=== diagrams.net 绘制步骤 ===

1. 创建新图表
   - 文件名：图2_DNB1101BB芯片连接电路图
   - 类型：电路图/网络图

2. 使用形状库：
   - 更多形状 → 电气 → 电路图符号
   - 或使用基本矩形自定义

3. 布局设计（从左到右）：

中央芯片区域：
- [大矩形] DNB1101BB芯片
  内部引脚（从上到下）：
  - VDD (电源正)
  - VSS (电源负)  
  - REFIN+ (参考电压正)
  - REFIN- (参考电压负)
  - AIN0 (模拟输入0)
  - AIN1 (模拟输入1)
  - SCLK (SPI时钟)
  - MISO (SPI主入从出)
  - MOSI (SPI主出从入)
  - CS (SPI片选)

左侧电路区域：
电源电路组：
- [矩形] +3.3V → 连接到VDD
- [矩形] GND → 连接到VSS

参考电压电路组：
- [矩形] +2.5V基准 → 连接到REFIN+
- [矩形] GND → 连接到REFIN-

信号输入电路组：
- [矩形] 电压信号+ → 连接到AIN0
- [矩形] 电压信号- → 连接到AIN1

右侧电路区域：
SPI通信接口组：
- [矩形] MCU_SCLK → 连接到SCLK
- [矩形] MCU_MISO ← 连接自MISO
- [矩形] MCU_MOSI → 连接到MOSI
- [矩形] MCU_CS → 连接到CS

下方外围电路：
- [小矩形] 滤波电容C1 (100nF)
- [小矩形] 滤波电容C2 (10μF)  
- [小矩形] 去耦电容C3 (1μF)

4. 连接线类型：
- 电源线：粗实线（红色/黑色）
- 信号线：细实线（蓝色）
- 地线：虚线（黑色）

5. 颜色编码：
- VDD引脚：红色 (#ffcdd2)
- VSS引脚：绿色 (#c8e6c9)
- 参考电压：黄色 (#fff9c4)
- 信号输入：蓝色 (#e1f5fe)

=== LibreOffice Draw 绘制步骤 ===

1. 页面设置：
   - A4横向
   - 标题：图2_DNB1101BB芯片连接电路图

2. 绘制芯片主体：
   - 使用矩形工具绘制大矩形
   - 标注"DNB1101BB芯片"
   - 在边缘添加引脚标识

3. 引脚绘制：
   - 使用小矩形或线条表示引脚
   - 在引脚旁标注名称和功能

4. 外围电路：
   - 使用标准电路符号
   - 电阻：锯齿线
   - 电容：两条平行线
   - 电源：圆圈内加+/-

5. 连接线绘制：
   - 使用连接器工具
   - 保持线条整齐，避免交叉
   - 在关键节点添加连接点

=== 电路图绘制规范 ===

1. 引脚编号：
   - 按照实际芯片引脚顺序
   - 功能相关的引脚分组

2. 信号流向：
   - 输入信号从左侧进入
   - 输出信号从右侧输出
   - 电源从上方供给

3. 标注规范：
   - 电压值：+3.3V, +2.5V
   - 电容值：100nF, 10μF, 1μF
   - 信号名：使用标准命名

4. 线条规范：
   - 电源线：2pt粗线
   - 信号线：1pt细线
   - 地线：虚线或特殊标记

=== 技术参数标注 ===

1. 电源规格：
   - VDD: +3.3V ±5%
   - 电流消耗: <10mA

2. 参考电压：
   - VREF: +2.5V ±0.1%
   - 温度系数: <10ppm/°C

3. 信号范围：
   - 输入电压: 0-VREF
   - 分辨率: 24位

4. 通信接口：
   - SPI模式: Mode 0
   - 时钟频率: 最大10MHz

=== 保存格式 ===

diagrams.net:
- 文件 → 导出为 → PNG/PDF
- 或保存为.drawio格式

LibreOffice Draw:
- 文件 → 另存为 → .odg格式
- 或导出为PDF/PNG
