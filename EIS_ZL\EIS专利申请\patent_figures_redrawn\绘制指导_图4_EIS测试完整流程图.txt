图4_EIS测试完整流程图 - 详细绘制指导

=== diagrams.net 绘制步骤 ===

1. 创建新图表
   - 文件名：图4_EIS测试完整流程图
   - 类型：流程图

2. 形状类型说明：
   - 开始/结束：椭圆形（颜色：#c8e6c9）
   - 处理步骤：矩形（颜色：白色）
   - 判断条件：菱形（颜色：#fff9c4）
   - 重要算法：矩形（颜色：#e1f5fe）

3. 流程步骤（从上到下）：

开始流程：
- [椭圆] 开始

初始化阶段：
- [矩形] 系统初始化
- [矩形] 设备自检
- [菱形] 自检通过?
  - 否 → [矩形] 显示错误信息 → [椭圆] 结束
  - 是 → 继续

参数设置阶段：
- [矩形] 设置测试参数
- [矩形] 频率范围设置
- [矩形] 扫描点数设置
- [矩形] 激励电流设置

测试执行阶段：
- [矩形] 开始频率扫描
- [矩形] 设置当前频率
- [矩形] 输出激励信号
- [矩形] 采集响应信号
- [矩形] 计算阻抗值
- [矩形] 存储测量数据
- [菱形] 是否为最后频率点?
  - 否 → [矩形] 切换到下一频率 → 回到"设置当前频率"
  - 是 → 继续

数据处理阶段：
- [矩形] 数据预处理
- [矩形，#e1f5fe] 多维参数提取
- [矩形，#e1f5fe] 九档智能分组
- [矩形，#e1f5fe] 增益自适应调节

结果输出阶段：
- [矩形] 生成测试报告
- [矩形] 保存测试结果
- [矩形] 显示奈奎斯特曲线
- [菱形] 继续测试?
  - 是 → 回到"设置测试参数"
  - 否 → [椭圆] 结束

4. 连接线设置：
- 主流程：实线箭头
- 循环回路：虚线箭头
- 判断分支：标注"是"/"否"

=== LibreOffice Draw 绘制步骤 ===

1. 页面设置：
   - A4纵向
   - 标题：图4_EIS测试完整流程图

2. 绘制工具选择：
   - 椭圆工具：开始/结束
   - 矩形工具：处理步骤
   - 菱形工具：判断条件（基本形状 → 菱形）

3. 布局建议：
   - 垂直流程为主
   - 判断分支向右展开
   - 循环回路用弧形连接线

4. 文字设置：
   - 字体：宋体，10pt
   - 对齐：居中
   - 颜色：黑色

=== 特殊注意事项 ===

1. 循环结构：
   - "是否为最后频率点?" 的"否"分支要回到"设置当前频率"
   - "继续测试?" 的"是"分支要回到"设置测试参数"

2. 颜色编码：
   - 绿色：开始/结束节点
   - 红色：错误/异常节点
   - 黄色：判断节点
   - 蓝色：核心算法节点

3. 连接线标注：
   - 在判断节点的分支上标注"是"/"否"
   - 循环回路可以用不同颜色区分

4. 整体布局：
   - 保持流程的清晰性
   - 避免连接线交叉
   - 相同类型的节点保持一致的大小
