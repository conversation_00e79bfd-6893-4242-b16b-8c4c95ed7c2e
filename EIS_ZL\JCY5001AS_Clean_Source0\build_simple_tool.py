#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A简化授权工具打包脚本
将简化版授权工具打包为独立的EXE文件

Author: Augment Agent
Date: 2025-01-09
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def build_simple_exe():
    """构建简化版EXE文件"""
    print("🚀 开始构建JCY5001A简化授权工具...")
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 构建命令
    build_cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",                        # 打包为单个文件
        "--windowed",                       # 无控制台窗口
        "--name=JCY5001A_简化授权工具",       # 输出文件名
        "--distpath=dist_simple",           # 输出目录
        "--workpath=build_simple",          # 临时构建目录
        "--specpath=.",                     # spec文件位置
        "--clean",                          # 清理临时文件
        "--noconfirm",                      # 不询问覆盖
        "simple_license_tool.py"            # 源文件
    ]
    
    # 添加隐藏导入
    hidden_imports = [
        "PyQt5.QtCore",
        "PyQt5.QtWidgets", 
        "PyQt5.QtGui",
        "hashlib",
        "base64",
        "datetime"
    ]
    
    for module in hidden_imports:
        build_cmd.extend(["--hidden-import", module])
    
    # 执行构建
    try:
        print("执行构建命令:")
        print(" ".join(build_cmd))
        print("\n" + "="*50)
        
        result = subprocess.run(build_cmd, check=True, capture_output=False)
        
        print("="*50)
        print("✅ 构建完成！")
        
        # 检查输出文件
        exe_path = Path("dist_simple") / "JCY5001A_简化授权工具.exe"
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"📁 输出文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 创建使用说明
            create_simple_readme()
            
            return True
        else:
            print("❌ 未找到输出的EXE文件")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False

def create_simple_readme():
    """创建简化版使用说明"""
    readme_content = """# 🔑 JCY5001A EIS阻抗筛选仪 - 简化授权工具

## 📋 工具说明

这是JCY5001A EIS阻抗筛选仪的简化授权工具，专门用于快速生成和验证解锁码。

## 🎯 使用流程

### 👤 用户端操作：
1. 用户运行JCY5001A软件
2. 遇到授权错误时，软件显示机器指纹码
3. 用户将指纹码发送给您

### 🔧 管理员操作：
1. 打开简化授权工具
2. 在"生成解锁码"区域输入用户提供的指纹码
3. 设置授权参数（天数、通道数、版本）
4. 点击"生成解锁码"按钮
5. 复制生成的解锁码发送给用户

### 👤 用户解锁：
1. 用户在软件中输入您提供的解锁码
2. 软件验证通过后正常使用

## 🔑 功能特点

### ✅ 生成解锁码
- **输入**: 机器指纹码（如：583E-BC4F-92C8-F89D-10DC）
- **配置**: 授权天数、最大通道数、产品版本
- **输出**: 格式化解锁码（如：A1B2-C3D4-E5F6-G7H8）
- **信息**: 显示详细授权信息和到期时间

### ✅ 验证解锁码
- **验证**: 检查解锁码是否有效
- **状态**: 显示授权状态和剩余时间
- **调试**: 用于排查授权问题

## 📝 操作示例

### 场景：用户请求授权

1. **用户反馈**：
   ```
   软件提示：授权错误，请联系供应商
   机器指纹码：583E-BC4F-92C8-F89D-10DC
   ```

2. **您的操作**：
   - 打开简化授权工具
   - 输入指纹码：`583E-BC4F-92C8-F89D-10DC`
   - 设置授权天数：`365`（一年）
   - 设置通道数：`8`
   - 选择版本：`专业版`
   - 点击"生成解锁码"

3. **生成结果**：
   ```
   解锁码：A1B2-C3D4-E5F6-G7H8
   到期日期：2026-01-09
   ```

4. **发送给用户**：
   ```
   您的解锁码：A1B2-C3D4-E5F6-G7H8
   有效期：一年（到2026-01-09）
   请在软件中输入此解锁码
   ```

## ⚙️ 授权参数说明

### 授权天数
- **1-30天**: 试用授权
- **365天**: 标准一年授权
- **3650天**: 十年长期授权

### 最大通道数
- **1-8通道**: 标准配置
- **9-16通道**: 扩展配置
- **17-32通道**: 企业配置

### 产品版本
- **试用版**: 功能受限
- **标准版**: 基础功能
- **专业版**: 完整功能
- **企业版**: 高级功能

## 🔐 安全机制

- **指纹绑定**: 解锁码与机器硬件绑定
- **时效控制**: 自动过期保护
- **加密算法**: SHA256哈希加密
- **格式验证**: 防止错误输入

## ⚠️ 注意事项

1. **指纹码准确性**: 确保用户提供的指纹码完整准确
2. **解锁码格式**: 保持4-4-4-4的格式不变
3. **有效期管理**: 注意授权到期时间
4. **版本匹配**: 确保版本设置与用户需求匹配

## 🛠️ 故障排除

### 问题：解锁码无效
- 检查指纹码是否正确
- 确认解锁码格式完整
- 验证授权是否过期

### 问题：机器指纹变化
- 硬件更换可能导致指纹变化
- 需要重新获取新的指纹码
- 重新生成对应的解锁码

## 📞 技术支持

如有问题请联系：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

---
© 2025 鲸测云科技 版权所有
"""
    
    readme_path = Path("dist_simple") / "使用说明.md"
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"📝 使用说明已创建: {readme_path}")
    except Exception as e:
        print(f"⚠️ 创建使用说明失败: {e}")

def cleanup_simple():
    """清理临时文件"""
    print("清理临时文件...")
    
    cleanup_dirs = ["build_simple", "__pycache__"]
    
    for dir_name in cleanup_dirs:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print(f"🗑️ 已删除: {dir_name}")
            except Exception as e:
                print(f"⚠️ 删除失败 {dir_name}: {e}")
    
    # 删除spec文件
    import glob
    for spec_file in glob.glob("*简化授权工具.spec"):
        try:
            os.remove(spec_file)
            print(f"🗑️ 已删除: {spec_file}")
        except Exception as e:
            print(f"⚠️ 删除失败 {spec_file}: {e}")

def main():
    """主函数"""
    print("🔑 JCY5001A简化授权工具构建脚本")
    print("="*50)
    
    # 检查源文件
    if not os.path.exists("simple_license_tool.py"):
        print("❌ 未找到源文件 simple_license_tool.py")
        return False
    
    # 构建EXE
    success = build_simple_exe()
    
    # 清理临时文件
    cleanup_simple()
    
    if success:
        print("\n🎉 简化授权工具构建成功！")
        print("📁 输出目录: dist_simple/")
        print("🔧 可执行文件: JCY5001A_简化授权工具.exe")
        print("\n✅ 现在您可以使用简化授权工具快速生成解锁码了！")
    else:
        print("\n❌ 构建失败，请检查错误信息")
    
    return success

if __name__ == "__main__":
    main()
