# 🔑 JCY5001A EIS阻抗筛选仪 - 简化授权工具

## 📋 工具说明

这是JCY5001A EIS阻抗筛选仪的简化授权工具，专门用于快速生成和验证解锁码。

## 🎯 使用流程

### 👤 用户端操作：
1. 用户运行JCY5001A软件
2. 遇到授权错误时，软件显示机器指纹码
3. 用户将指纹码发送给您

### 🔧 管理员操作：
1. 打开简化授权工具
2. 在"生成解锁码"区域输入用户提供的指纹码
3. 设置授权参数（天数、通道数、版本）
4. 点击"生成解锁码"按钮
5. 复制生成的解锁码发送给用户

### 👤 用户解锁：
1. 用户在软件中输入您提供的解锁码
2. 软件验证通过后正常使用

## 🔑 功能特点

### ✅ 生成解锁码
- **输入**: 机器指纹码（如：583E-BC4F-92C8-F89D-10DC）
- **配置**: 授权天数、最大通道数、产品版本
- **输出**: 格式化解锁码（如：A1B2-C3D4-E5F6-G7H8）
- **信息**: 显示详细授权信息和到期时间

### ✅ 验证解锁码
- **验证**: 检查解锁码是否有效
- **状态**: 显示授权状态和剩余时间
- **调试**: 用于排查授权问题

## 📝 操作示例

### 场景：用户请求授权

1. **用户反馈**：
   ```
   软件提示：授权错误，请联系供应商
   机器指纹码：583E-BC4F-92C8-F89D-10DC
   ```

2. **您的操作**：
   - 打开简化授权工具
   - 输入指纹码：`583E-BC4F-92C8-F89D-10DC`
   - 设置授权天数：`365`（一年）
   - 设置通道数：`8`
   - 选择版本：`专业版`
   - 点击"生成解锁码"

3. **生成结果**：
   ```
   解锁码：A1B2-C3D4-E5F6-G7H8
   到期日期：2026-01-09
   ```

4. **发送给用户**：
   ```
   您的解锁码：A1B2-C3D4-E5F6-G7H8
   有效期：一年（到2026-01-09）
   请在软件中输入此解锁码
   ```

## ⚙️ 授权参数说明

### 授权天数
- **1-30天**: 试用授权
- **365天**: 标准一年授权
- **3650天**: 十年长期授权

### 最大通道数
- **1-8通道**: 标准配置
- **9-16通道**: 扩展配置
- **17-32通道**: 企业配置

### 产品版本
- **试用版**: 功能受限
- **标准版**: 基础功能
- **专业版**: 完整功能
- **企业版**: 高级功能

## 🔐 安全机制

- **指纹绑定**: 解锁码与机器硬件绑定
- **时效控制**: 自动过期保护
- **加密算法**: SHA256哈希加密
- **格式验证**: 防止错误输入

## ⚠️ 注意事项

1. **指纹码准确性**: 确保用户提供的指纹码完整准确
2. **解锁码格式**: 保持4-4-4-4的格式不变
3. **有效期管理**: 注意授权到期时间
4. **版本匹配**: 确保版本设置与用户需求匹配

## 🛠️ 故障排除

### 问题：解锁码无效
- 检查指纹码是否正确
- 确认解锁码格式完整
- 验证授权是否过期

### 问题：机器指纹变化
- 硬件更换可能导致指纹变化
- 需要重新获取新的指纹码
- 重新生成对应的解锁码

## 📞 技术支持

如有问题请联系：
- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx

---
© 2025 鲸测云科技 版权所有
