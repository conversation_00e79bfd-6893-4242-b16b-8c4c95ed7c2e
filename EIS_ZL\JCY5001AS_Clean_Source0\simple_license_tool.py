#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JCY5001A EIS阻抗筛选仪简化授权工具
基于指纹码生成解锁码的简单授权系统

Author: Augment Agent
Date: 2025-01-09
Version: 1.0.0
"""

import sys
import hashlib
import base64
import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit,
                             QGroupBox, QGridLayout, QMessageBox, QComboBox,
                             QDateEdit, QSpinBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QFont, QIcon

class SimpleLicenseGenerator:
    """简化授权码生成器 - 使用与主程序相同的算法"""

    def __init__(self):
        # 主密钥（与主程序保持一致）
        self.master_key = "JCY5001A_EIS_MASTER_KEY_2025"

    def generate_unlock_code(self, fingerprint, days=365, unlock_type="full"):
        """
        根据指纹码生成解锁码 - 使用与主程序相同的算法

        Args:
            fingerprint: 机器指纹码
            days: 授权天数 (仅用于temp类型)
            unlock_type: 解锁类型 ('full', 'temp')

        Returns:
            解锁码字符串
        """
        try:
            # 构建解锁数据（与主程序LicenseManager._generate_unlock_code_string相同）
            unlock_data = {
                'customer_fingerprint': fingerprint,
                'customer_id': 'CUSTOMER_001',
                'unlock_type': unlock_type,
                'extend_days': days if unlock_type == 'temp' else 0,
                'generate_date': datetime.datetime.now().isoformat(),
                'generator': 'factory_tool'
            }

            # 第一段：硬件指纹校验码
            fingerprint_hash = hashlib.md5(unlock_data['customer_fingerprint'].encode()).hexdigest()[:8]

            # 第二段：解锁类型和天数
            if unlock_type == 'full':
                type_code = 'FFFF'
            elif unlock_type == 'temp':
                type_code = f'M{unlock_data["extend_days"]:03d}'
            else:
                type_code = 'FFFF'  # 默认为永久授权

            type_segment = f"{type_code}{'0' * (8 - len(type_code))}"[:8]

            # 第三段：客户ID和时间戳
            customer_hash = hashlib.md5(unlock_data['customer_id'].encode()).hexdigest()[:8]

            # 第四段：安全校验码
            verify_string = f"{fingerprint_hash}{type_segment}{customer_hash}{unlock_data['generate_date']}"
            verify_hash = hashlib.sha256(verify_string.encode()).hexdigest()[:8]

            # 组合解锁码
            unlock_code = f"{fingerprint_hash}-{type_segment}-{customer_hash}-{verify_hash}".upper()

            return unlock_code

        except Exception as e:
            raise Exception(f"生成解锁码失败: {str(e)}")

    def verify_unlock_code(self, fingerprint, unlock_code):
        """
        验证解锁码是否有效 - 使用与主程序相同的验证逻辑

        Args:
            fingerprint: 机器指纹码
            unlock_code: 解锁码

        Returns:
            (是否有效, 解锁类型或错误信息)
        """
        try:
            # 移除分隔符并转换为大写
            clean_code = unlock_code.replace("-", "").replace(" ", "").upper()

            if len(clean_code) != 32:
                return False, '解锁码格式错误'

            # 分解解锁码
            fingerprint_part = clean_code[:8]
            type_part = clean_code[8:16]
            customer_part = clean_code[16:24]
            verify_part = clean_code[24:32]

            # 验证硬件指纹
            expected_fingerprint_hash = hashlib.md5(fingerprint.encode()).hexdigest()[:8].upper()

            if fingerprint_part != expected_fingerprint_hash:
                return False, '解锁码与当前设备不匹配'

            # 解析解锁类型
            unlock_type = None
            extend_days = 0

            if type_part == 'FFFF0000':
                unlock_type = 'full'
                unlock_type_name = '永久授权'
            elif type_part.startswith('T'):
                unlock_type = 'trial_extend'
                try:
                    extend_days = int(type_part[1:4])
                    unlock_type_name = f'试用期延长 {extend_days} 天'
                except:
                    return False, '解锁码格式错误'
            elif type_part.startswith('M'):
                unlock_type = 'temp'
                try:
                    extend_days = int(type_part[1:4])
                    unlock_type_name = f'临时授权 {extend_days} 天'
                except:
                    return False, '解锁码格式错误'
            else:
                return False, '不支持的解锁类型'

            return True, f'解锁码验证成功 - {unlock_type_name}'

        except Exception as e:
            return False, f"验证失败: {str(e)}"

class SimpleLicenseToolWindow(QMainWindow):
    """简化授权工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.license_generator = SimpleLicenseGenerator()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JCY5001A EIS阻抗筛选仪 - 简化授权工具 v1.0.0")
        self.setGeometry(200, 200, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标题
        title_label = QLabel("JCY5001A EIS阻抗筛选仪简化授权工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #2c3e50; margin: 15px; padding: 10px;")
        main_layout.addWidget(title_label)
        
        # 创建生成解锁码组
        self.create_generate_group(main_layout)
        
        # 创建验证解锁码组
        self.create_verify_group(main_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #007bff;
                border-radius: 8px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #007bff;
                font-size: 14px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton.success {
                background-color: #28a745;
            }
            QPushButton.success:hover {
                background-color: #218838;
            }
            QPushButton.danger {
                background-color: #dc3545;
            }
            QPushButton.danger:hover {
                background-color: #c82333;
            }
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDateEdit {
                border: 2px solid #ced4da;
                padding: 8px;
                border-radius: 6px;
                font-size: 12px;
            }
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #007bff;
            }
            QLabel {
                font-size: 12px;
                color: #495057;
            }
        """)
    
    def create_generate_group(self, main_layout):
        """创建生成解锁码组"""
        generate_group = QGroupBox("🔑 生成解锁码")
        generate_layout = QGridLayout(generate_group)
        
        # 指纹码输入
        generate_layout.addWidget(QLabel("机器指纹码:"), 0, 0)
        self.fingerprint_input = QLineEdit()
        self.fingerprint_input.setPlaceholderText("请输入用户提供的机器指纹码，如：583E-BC4F-92C8-F89D-10DC")
        generate_layout.addWidget(self.fingerprint_input, 0, 1, 1, 3)
        
        # 授权配置
        generate_layout.addWidget(QLabel("授权天数:"), 1, 0)
        self.days_spin = QSpinBox()
        self.days_spin.setRange(1, 3650)
        self.days_spin.setValue(365)
        self.days_spin.setSuffix(" 天")
        generate_layout.addWidget(self.days_spin, 1, 1)
        
        generate_layout.addWidget(QLabel("最大通道数:"), 1, 2)
        self.channels_spin = QSpinBox()
        self.channels_spin.setRange(1, 32)
        self.channels_spin.setValue(8)
        generate_layout.addWidget(self.channels_spin, 1, 3)
        
        generate_layout.addWidget(QLabel("产品版本:"), 2, 0)
        self.version_combo = QComboBox()
        self.version_combo.addItems(["试用版", "标准版", "专业版", "企业版"])
        self.version_combo.setCurrentText("专业版")
        generate_layout.addWidget(self.version_combo, 2, 1)
        
        # 生成按钮
        generate_btn = QPushButton("🔑 生成解锁码")
        generate_btn.setProperty("class", "success")
        generate_btn.clicked.connect(self.generate_unlock_code)
        generate_layout.addWidget(generate_btn, 2, 2, 1, 2)
        
        # 解锁码输出
        generate_layout.addWidget(QLabel("生成的解锁码:"), 3, 0)
        self.unlock_code_output = QLineEdit()
        self.unlock_code_output.setReadOnly(True)
        self.unlock_code_output.setStyleSheet("background-color: #e9ecef; font-weight: bold; font-size: 14px;")
        generate_layout.addWidget(self.unlock_code_output, 3, 1, 1, 2)
        
        copy_btn = QPushButton("📋 复制")
        copy_btn.clicked.connect(self.copy_unlock_code)
        generate_layout.addWidget(copy_btn, 3, 3)
        
        # 授权信息显示
        generate_layout.addWidget(QLabel("授权信息:"), 4, 0)
        self.auth_info_display = QTextEdit()
        self.auth_info_display.setReadOnly(True)
        self.auth_info_display.setMaximumHeight(80)
        generate_layout.addWidget(self.auth_info_display, 4, 1, 1, 3)
        
        main_layout.addWidget(generate_group)
    
    def create_verify_group(self, main_layout):
        """创建验证解锁码组"""
        verify_group = QGroupBox("✅ 验证解锁码")
        verify_layout = QGridLayout(verify_group)
        
        # 指纹码输入
        verify_layout.addWidget(QLabel("机器指纹码:"), 0, 0)
        self.verify_fingerprint_input = QLineEdit()
        self.verify_fingerprint_input.setPlaceholderText("请输入机器指纹码")
        verify_layout.addWidget(self.verify_fingerprint_input, 0, 1, 1, 2)
        
        # 解锁码输入
        verify_layout.addWidget(QLabel("解锁码:"), 1, 0)
        self.verify_unlock_code_input = QLineEdit()
        self.verify_unlock_code_input.setPlaceholderText("请输入解锁码，如：A1B2-C3D4-E5F6-G7H8")
        verify_layout.addWidget(self.verify_unlock_code_input, 1, 1, 1, 2)
        
        # 验证按钮
        verify_btn = QPushButton("✅ 验证解锁码")
        verify_btn.clicked.connect(self.verify_unlock_code)
        verify_layout.addWidget(verify_btn, 2, 0, 1, 3)
        
        # 验证结果显示
        verify_layout.addWidget(QLabel("验证结果:"), 3, 0)
        self.verify_result_display = QTextEdit()
        self.verify_result_display.setReadOnly(True)
        self.verify_result_display.setMaximumHeight(100)
        verify_layout.addWidget(self.verify_result_display, 3, 1, 1, 2)
        
        main_layout.addWidget(verify_group)
    
    def generate_unlock_code(self):
        """生成解锁码"""
        try:
            fingerprint = self.fingerprint_input.text().strip()
            if not fingerprint:
                QMessageBox.warning(self, "警告", "请输入机器指纹码")
                return

            days = self.days_spin.value()

            # 确定解锁类型
            unlock_type = "full" if days >= 3650 else "temp"

            # 生成解锁码
            unlock_code = self.license_generator.generate_unlock_code(
                fingerprint, days, unlock_type
            )

            # 显示解锁码
            self.unlock_code_output.setText(unlock_code)

            # 显示授权信息
            if unlock_type == "full":
                type_name = "永久授权"
                expire_info = "永不过期"
            else:
                type_name = f"临时授权 {days} 天"
                expire_date = datetime.datetime.now() + datetime.timedelta(days=days)
                expire_info = expire_date.strftime('%Y-%m-%d %H:%M:%S')

            auth_info = f"授权类型: {type_name}\n"
            auth_info += f"授权天数: {days} 天\n"
            auth_info += f"到期时间: {expire_info}\n"
            auth_info += f"机器指纹: {fingerprint}"

            self.auth_info_display.setPlainText(auth_info)

            QMessageBox.information(self, "成功", f"解锁码生成成功！\n\n解锁码: {unlock_code}\n授权类型: {type_name}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"生成解锁码失败: {str(e)}")
    
    def copy_unlock_code(self):
        """复制解锁码到剪贴板"""
        unlock_code = self.unlock_code_output.text()
        if unlock_code:
            clipboard = QApplication.clipboard()
            clipboard.setText(unlock_code)
            QMessageBox.information(self, "成功", "解锁码已复制到剪贴板")
        else:
            QMessageBox.warning(self, "警告", "没有解锁码可复制")
    
    def verify_unlock_code(self):
        """验证解锁码"""
        try:
            fingerprint = self.verify_fingerprint_input.text().strip()
            unlock_code = self.verify_unlock_code_input.text().strip()

            if not fingerprint:
                QMessageBox.warning(self, "警告", "请输入机器指纹码")
                return

            if not unlock_code:
                QMessageBox.warning(self, "警告", "请输入解锁码")
                return

            # 验证解锁码
            is_valid, result = self.license_generator.verify_unlock_code(fingerprint, unlock_code)

            if is_valid:
                result_text = f"✅ 解锁码验证成功！\n\n"
                result_text += f"机器指纹: {fingerprint}\n"
                result_text += f"解锁码: {unlock_code}\n"
                result_text += f"验证结果: {result}"

                self.verify_result_display.setPlainText(result_text)
                self.verify_result_display.setStyleSheet("color: green; background-color: #d4edda;")

                QMessageBox.information(self, "验证成功", "解锁码有效！")
            else:
                result_text = f"❌ 解锁码验证失败！\n\n错误信息: {result}"
                self.verify_result_display.setPlainText(result_text)
                self.verify_result_display.setStyleSheet("color: red; background-color: #f8d7da;")

                QMessageBox.warning(self, "验证失败", f"解锁码无效: {result}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"验证过程出错: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("JCY5001A简化授权工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("鲸测云科技")
    
    # 创建主窗口
    window = SimpleLicenseToolWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
