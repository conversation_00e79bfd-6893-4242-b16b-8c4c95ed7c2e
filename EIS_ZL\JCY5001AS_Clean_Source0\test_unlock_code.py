#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试解锁码生成和验证
"""

import hashlib
import datetime

def generate_unlock_code(fingerprint, days=365, unlock_type="full"):
    """
    根据指纹码生成解锁码 - 使用与主程序相同的算法
    """
    try:
        # 构建解锁数据（与主程序LicenseManager._generate_unlock_code_string相同）
        unlock_data = {
            'customer_fingerprint': fingerprint,
            'customer_id': 'CUSTOMER_001',
            'unlock_type': unlock_type,
            'extend_days': days if unlock_type == 'temp' else 0,
            'generate_date': datetime.datetime.now().isoformat(),
            'generator': 'factory_tool'
        }
        
        # 第一段：硬件指纹校验码
        fingerprint_hash = hashlib.md5(unlock_data['customer_fingerprint'].encode()).hexdigest()[:8]
        
        # 第二段：解锁类型和天数
        if unlock_type == 'full':
            type_code = 'FFFF'
        elif unlock_type == 'temp':
            type_code = f'M{unlock_data["extend_days"]:03d}'
        else:
            type_code = 'FFFF'  # 默认为永久授权
            
        type_segment = f"{type_code}{'0' * (8 - len(type_code))}"[:8]
        
        # 第三段：客户ID和时间戳
        customer_hash = hashlib.md5(unlock_data['customer_id'].encode()).hexdigest()[:8]
        
        # 第四段：安全校验码
        verify_string = f"{fingerprint_hash}{type_segment}{customer_hash}{unlock_data['generate_date']}"
        verify_hash = hashlib.sha256(verify_string.encode()).hexdigest()[:8]
        
        # 组合解锁码
        unlock_code = f"{fingerprint_hash}-{type_segment}-{customer_hash}-{verify_hash}".upper()
        
        return unlock_code
        
    except Exception as e:
        raise Exception(f"生成解锁码失败: {str(e)}")

def verify_unlock_code(fingerprint, unlock_code):
    """
    验证解锁码是否有效 - 使用与主程序相同的验证逻辑
    """
    try:
        # 移除分隔符并转换为大写
        clean_code = unlock_code.replace("-", "").replace(" ", "").upper()
        
        if len(clean_code) != 32:
            return False, '解锁码格式错误'
        
        # 分解解锁码
        fingerprint_part = clean_code[:8]
        type_part = clean_code[8:16]
        customer_part = clean_code[16:24]
        verify_part = clean_code[24:32]
        
        # 验证硬件指纹
        expected_fingerprint_hash = hashlib.md5(fingerprint.encode()).hexdigest()[:8].upper()
        
        if fingerprint_part != expected_fingerprint_hash:
            return False, '解锁码与当前设备不匹配'
        
        # 解析解锁类型
        unlock_type = None
        extend_days = 0
        
        if type_part == 'FFFF0000':
            unlock_type = 'full'
            unlock_type_name = '永久授权'
        elif type_part.startswith('T'):
            unlock_type = 'trial_extend'
            try:
                extend_days = int(type_part[1:4])
                unlock_type_name = f'试用期延长 {extend_days} 天'
            except:
                return False, '解锁码格式错误'
        elif type_part.startswith('M'):
            unlock_type = 'temp'
            try:
                extend_days = int(type_part[1:4])
                unlock_type_name = f'临时授权 {extend_days} 天'
            except:
                return False, '解锁码格式错误'
        else:
            return False, '不支持的解锁类型'
        
        return True, f'解锁码验证成功 - {unlock_type_name}'
        
    except Exception as e:
        return False, f"验证失败: {str(e)}"

if __name__ == "__main__":
    # 测试用户提供的指纹码
    fingerprint = "583Ebc4F92c8F89d10dce4b2cb83a4d161324dfcaaf29cb7ef77d1038e2"
    
    print("🔑 JCY5001A 解锁码生成测试")
    print("=" * 50)
    print(f"机器指纹码: {fingerprint}")
    print()
    
    # 生成永久授权解锁码
    print("1. 生成永久授权解锁码:")
    unlock_code_full = generate_unlock_code(fingerprint, unlock_type="full")
    print(f"   解锁码: {unlock_code_full}")
    
    # 验证解锁码
    is_valid, result = verify_unlock_code(fingerprint, unlock_code_full)
    print(f"   验证结果: {'✅ ' + result if is_valid else '❌ ' + result}")
    print()
    
    # 生成365天临时授权解锁码
    print("2. 生成365天临时授权解锁码:")
    unlock_code_temp = generate_unlock_code(fingerprint, days=365, unlock_type="temp")
    print(f"   解锁码: {unlock_code_temp}")
    
    # 验证解锁码
    is_valid, result = verify_unlock_code(fingerprint, unlock_code_temp)
    print(f"   验证结果: {'✅ ' + result if is_valid else '❌ ' + result}")
    print()
    
    print("🎯 请使用上述解锁码在主程序中测试！")
