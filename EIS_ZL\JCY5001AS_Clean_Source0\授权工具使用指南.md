# 🔐 JCY5001A EIS阻抗筛选仪授权管理工具

## ✅ **构建完成状态**

**✅ 授权管理工具已成功构建为EXE文件！**

- **📁 文件位置**: `dist_license\JCY5001A_授权管理工具.exe`
- **📊 文件大小**: 39.0 MB
- **🎯 状态**: 可直接运行，无需安装Python环境

## 🚀 **工具功能概览**

### 1. **生成许可证** 📝
- 输入客户信息（姓名、联系方式等）
- 配置许可证参数（版本、类型、有效期等）
- 设置功能权限和通道数限制
- 可选择绑定特定机器ID
- 生成加密的许可证文件

### 2. **验证许可证** ✅
- 导入许可证文件或直接输入许可证内容
- 验证许可证的有效性和完整性
- 检查机器绑定和有效期
- 显示详细的许可证信息

### 3. **机器信息** 💻
- 获取当前机器的唯一标识
- 显示详细的硬件和系统信息
- 用于许可证的机器绑定功能

### 4. **批量操作** 📊
- 批量生成多个许可证（开发中）
- 许可证管理和统计功能

## 🎯 **使用场景**

### **场景1：为新客户生成授权**
1. 打开授权管理工具
2. 在"生成许可证"选项卡中填写客户信息
3. 设置产品版本为"专业版"，授权类型为"永久授权"
4. 设置最大通道数为8（标准配置）
5. 获取客户机器ID并绑定（可选）
6. 生成并保存许可证文件
7. 将.lic文件发送给客户

### **场景2：验证现有许可证**
1. 在"验证许可证"选项卡中加载许可证文件
2. 点击验证按钮查看许可证状态
3. 确认有效期、机器绑定等信息
4. 排查授权问题

### **场景3：临时试用授权**
1. 设置授权类型为"试用授权"
2. 设置较短的有效期（如30天）
3. 限制功能权限
4. 生成试用许可证

## 🔧 **详细操作步骤**

### **生成许可证步骤**：

1. **启动工具**
   - 双击 `JCY5001A_授权管理工具.exe`
   - 等待界面加载完成

2. **填写客户信息**
   ```
   客户名称: 示例科技有限公司 [必填]
   联系人: 张工程师
   联系电话: 138-0000-0000
   邮箱地址: <EMAIL>
   ```

3. **配置许可证参数**
   ```
   产品版本: 专业版
   授权类型: 永久授权
   有效期至: 2026-01-09 (一年后)
   最大通道数: 8
   ```

4. **机器绑定（可选）**
   - 点击"获取当前机器ID"按钮
   - 复制机器ID到客户端
   - 或留空表示不绑定机器

5. **生成许可证**
   - 点击"生成许可证"按钮
   - 查看生成的加密许可证内容
   - 点击"保存许可证文件"
   - 选择保存位置，文件名如：`license_20250109_143000.lic`

### **验证许可证步骤**：

1. **加载许可证**
   - 切换到"验证许可证"选项卡
   - 点击"从文件加载"选择.lic文件
   - 或直接粘贴许可证内容

2. **执行验证**
   - 点击"验证许可证"按钮
   - 查看验证结果

3. **查看详细信息**
   ```
   ✅ 许可证验证成功！
   
   许可证信息:
   客户名称: 示例科技有限公司
   产品版本: 专业版
   授权类型: 永久授权
   有效期至: 2026-01-09
   最大通道数: 8
   机器ID: A1B2C3D4E5F6G7H8
   创建时间: 2025-01-09T14:30:00
   ```

## 🔐 **安全特性**

### **加密保护**
- 使用AES-256加密算法
- Base64编码便于传输
- 防止许可证内容被篡改

### **机器绑定**
- 基于硬件特征生成唯一机器ID
- 包含：MAC地址、处理器、主板等信息
- 防止许可证在不同机器间滥用

### **时效验证**
- 内置时间戳验证
- 自动检查许可证有效期
- 过期自动失效

## ⚠️ **重要注意事项**

### **许可证管理**
1. **备份重要**：请备份所有生成的许可证文件
2. **安全存储**：许可证文件包含敏感信息，请妥善保管
3. **版本兼容**：确保许可证版本与软件版本匹配

### **机器绑定**
1. **硬件变更**：更换主要硬件可能导致机器ID变化
2. **虚拟机**：虚拟机环境的机器ID可能不稳定
3. **网络环境**：机器ID不依赖网络连接

### **故障排除**
1. **许可证无效**：检查文件完整性和格式
2. **机器ID不匹配**：重新获取当前机器ID
3. **过期问题**：检查系统时间和许可证有效期

## 📞 **技术支持**

如遇到问题，请联系：
- **邮箱**: <EMAIL>
- **电话**: 400-xxx-xxxx
- **QQ群**: 123456789

## 📋 **文件清单**

```
dist_license/
├── JCY5001A_授权管理工具.exe    # 主程序文件 (39.0 MB)
├── README.md                    # 详细使用说明
└── 授权工具使用指南.md           # 本文件
```

## 🎉 **总结**

**JCY5001A授权管理工具**已成功构建为独立的EXE文件，具备以下特点：

✅ **即开即用** - 无需安装Python环境  
✅ **功能完整** - 支持生成、验证、管理许可证  
✅ **安全可靠** - 采用企业级加密算法  
✅ **界面友好** - 直观的图形化操作界面  
✅ **灵活配置** - 支持多种授权类型和参数  

现在您可以使用这个工具来管理JCY5001A EIS阻抗筛选仪的软件授权了！

---
**© 2025 鲸测云科技 版权所有**
