document.addEventListener('DOMContentLoaded', () => {
    const modelSelect = document.getElementById('modelSelect');
    const refreshModels = document.getElementById('refreshModels');
    const streamMode = document.getElementById('streamMode');
    const statusDot = document.querySelector('.status-dot');
    const statusText = document.getElementById('statusText');
    const chatMessages = document.getElementById('chatMessages');
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const clearChat = document.getElementById('clearChat');
    const stopGeneration = document.getElementById('stopGeneration');

    let currentModel = '';
    let controller = null;

    async function fetchModels() {
        modelSelect.innerHTML = `<option value="">加载中...</option>`;
        try {
            const res = await fetch('/api/models');
            const data = await res.json();
            modelSelect.innerHTML = '';
            if (data.models && data.models.length > 0) {
                data.models.forEach(m => {
                    const opt = document.createElement('option');
                    opt.value = m.name;
                    opt.textContent = m.name;
                    modelSelect.appendChild(opt);
                });
                currentModel = data.models[0].name;
                modelSelect.value = currentModel;
            } else {
                modelSelect.innerHTML = `<option value="">无可用模型</option>`;
            }
        } catch (e) {
            modelSelect.innerHTML = `<option value="">加载失败</option>`;
        }
    }

    async function checkStatus() {
        try {
            const res = await fetch('/api/models');
            if (res.ok) {
                statusDot.classList.add('online');
                statusDot.classList.remove('offline');
                statusText.textContent = '在线';
            } else {
                throw new Error();
            }
        } catch {
            statusDot.classList.remove('online');
            statusDot.classList.add('offline');
            statusText.textContent = '离线';
        }
    }

    function appendMessage(role, content) {
        const msg = document.createElement('div');
        msg.className = `message ${role}`;
        const msgContent = document.createElement('div');
        msgContent.className = 'message-content';
        msgContent.textContent = content;
        msg.appendChild(msgContent);
        chatMessages.appendChild(msg);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    async function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || !currentModel) return;
        appendMessage('user', message);
        messageInput.value = '';
        sendButton.disabled = true;

        if (streamMode.checked) {
            appendMessage('system', 'AI 正在思考...');
            controller = new AbortController();
            try {
                const res = await fetch('/api/chat/stream', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ model: currentModel, message }),
                    signal: controller.signal
                });
                if (!res.body) throw new Error('无响应流');
                let aiMsg = '';
                const reader = res.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    buffer += decoder.decode(value, { stream: true });
                    let lines = buffer.split('\n');
                    buffer = lines.pop(); // 最后一行可能不完整，留到下次
                    for (const line of lines) {
                        if (!line.trim()) continue;
                        try {
                            const obj = JSON.parse(line);
                            if (obj.response) aiMsg += obj.response;
                        } catch (e) {
                            // 忽略解析失败的行
                        }
                    }
                    chatMessages.lastChild.querySelector('.message-content').textContent = aiMsg;
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }
                chatMessages.lastChild.querySelector('.message-content').textContent = aiMsg;
            } catch (e) {
                if (e.name === 'AbortError') {
                    chatMessages.lastChild.querySelector('.message-content').textContent = '生成已停止。';
                } else {
                    chatMessages.lastChild.querySelector('.message-content').textContent = '生成出错，请重试。';
                }
            }
            controller = null;
        } else {
            appendMessage('system', 'AI 正在思考...');
            try {
                const res = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ model: currentModel, message })
                });
                const data = await res.json();
                chatMessages.lastChild.querySelector('.message-content').textContent = data.response || '无响应';
            } catch (e) {
                chatMessages.lastChild.querySelector('.message-content').textContent = '请求失败，请重试。';
            }
        }
        sendButton.disabled = false;
    }

    modelSelect.addEventListener('change', e => {
        currentModel = e.target.value;
    });
    refreshModels.addEventListener('click', fetchModels);
    sendButton.addEventListener('click', sendMessage);
    messageInput.addEventListener('input', () => {
        sendButton.disabled = !messageInput.value.trim();
    });
    messageInput.addEventListener('keydown', e => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });
    clearChat.addEventListener('click', () => {
        chatMessages.innerHTML = `
            <div class="message system">
                <div class="message-content">
                    欢迎使用 Ollama AI 聊天助手！请选择模型并开始对话。
                </div>
            </div>
        `;
    });
    stopGeneration.addEventListener('click', () => {
        if (controller) {
            controller.abort();
            controller = null;
        }
    });

    // 移除AI图片生成相关JS

    // 代码生成弹窗逻辑
    const codeGenBtn = document.getElementById('codeGenBtn');
    const codeGenModal = document.getElementById('codeGenModal');
    const closeCodeGen = document.getElementById('closeCodeGen');
    const codeLang = document.getElementById('codeLang');
    const codeGenPrompt = document.getElementById('codeGenPrompt');
    const codeGenSubmit = document.getElementById('codeGenSubmit');
    const codeGenResult = document.getElementById('codeGenResult');

    codeGenBtn && codeGenBtn.addEventListener('click', () => {
        codeGenModal.style.display = 'flex';
        codeGenPrompt.value = '';
        codeGenResult.textContent = '';
    });
    closeCodeGen && closeCodeGen.addEventListener('click', () => {
        codeGenModal.style.display = 'none';
    });
    codeGenSubmit && codeGenSubmit.addEventListener('click', async () => {
        const prompt = codeGenPrompt.value.trim();
        const lang = codeLang.value;
        if (!prompt) return;
        codeGenResult.textContent = '正在生成代码，请稍候...';
        try {
            const res = await fetch('/api/codegen', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, lang, model: currentModel })
            });
            const data = await res.json();
            if (data && data.code) {
                codeGenResult.textContent = data.code;
            } else {
                codeGenResult.textContent = '代码生成失败，请重试。';
            }
        } catch {
            codeGenResult.textContent = '代码生成失败，请重试。';
        }
    });

    // 代码导出为文件
    const codeGenExport = document.getElementById('codeGenExport');
    codeGenExport && codeGenExport.addEventListener('click', () => {
        const code = codeGenResult.textContent;
        const lang = codeLang.value;
        if (!code) return alert('没有可导出的代码！');
        let ext = 'txt';
        if (lang === 'c') ext = 'c';
        else if (lang === 'cpp') ext = 'cpp';
        else if (lang === 'csharp') ext = 'cs';
        else if (lang === 'python') ext = 'py';
        const blob = new Blob([code], { type: 'text/plain' });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = `weiwei_code.${ext}`;
        a.click();
        URL.revokeObjectURL(a.href);
    });

    // 办公助手弹窗逻辑
    const officeBtn = document.getElementById('officeBtn');
    const officeModal = document.getElementById('officeModal');
    const closeOffice = document.getElementById('closeOffice');
    const officePrompt = document.getElementById('officePrompt');
    const officeSubmit = document.getElementById('officeSubmit');
    const officeResult = document.getElementById('officeResult');

    officeBtn && officeBtn.addEventListener('click', () => {
        officeModal.style.display = 'flex';
        officePrompt.value = '';
        officeResult.textContent = '';
    });
    closeOffice && closeOffice.addEventListener('click', () => {
        officeModal.style.display = 'none';
    });
    officeSubmit && officeSubmit.addEventListener('click', async () => {
        const prompt = officePrompt.value.trim();
        if (!prompt) return;
        officeResult.textContent = '正在生成内容，请稍候...';
        try {
            const res = await fetch('/api/office', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ prompt, model: currentModel })
            });
            const data = await res.json();
            if (data && data.content) {
                officeResult.textContent = data.content;
            } else {
                officeResult.textContent = '内容生成失败，请重试。';
            }
        } catch {
            officeResult.textContent = '内容生成失败，请重试。';
        }
    });

    // 办公助手导出
    const officeExportWord = document.getElementById('officeExportWord');
    const officeExportExcel = document.getElementById('officeExportExcel');
    const officeExportPDF = document.getElementById('officeExportPDF');
    officeExportWord && officeExportWord.addEventListener('click', () => {
        const content = officeResult.textContent;
        if (!content) return alert('没有可导出的内容！');
        const blob = new Blob([content], { type: 'application/msword' });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = 'weiwei_office.doc';
        a.click();
        URL.revokeObjectURL(a.href);
    });
    officeExportExcel && officeExportExcel.addEventListener('click', () => {
        const content = officeResult.textContent;
        if (!content) return alert('没有可导出的内容！');
        const blob = new Blob([content], { type: 'application/vnd.ms-excel' });
        const a = document.createElement('a');
        a.href = URL.createObjectURL(blob);
        a.download = 'weiwei_office.xls';
        a.click();
        URL.revokeObjectURL(a.href);
    });
    officeExportPDF && officeExportPDF.addEventListener('click', () => {
        const content = officeResult.textContent;
        if (!content) return alert('没有可导出的内容！');
        // 简单文本转PDF（后续可用pdf-lib等库增强）
        const win = window.open('', '', 'height=700,width=700');
        win.document.write('<pre>' + content.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>');
        win.document.close();
        win.print();
    });

    fetchModels();
    checkStatus();
    setInterval(checkStatus, 10000); // 每10秒检查一次状态
}); 