const express = require('express');
const cors = require('cors');
const axios = require('axios');
const path = require('path');
require('dotenv').config();
const fs = require('fs');
const { Document, Packer, Paragraph } = require('docx');
const ExcelJS = require('exceljs');
const PDFDocument = require('pdfkit');

const app = express();
const PORT = process.env.PORT || 3000;

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

const OLLAMA_BASE_URL = process.env.OLLAMA_BASE_URL || 'http://localhost:11434';

app.get('/api/models', async (req, res) => {
    try {
        const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`);
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: '获取模型列表失败' });
    }
});

app.post('/api/chat', async (req, res) => {
    try {
        const { model, message, stream = false } = req.body;
        if (!model || !message) {
            return res.status(400).json({ error: '模型名称和消息内容不能为空' });
        }
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, {
            model: model,
            prompt: message,
            stream: stream
        }, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        res.json(response.data);
    } catch (error) {
        res.status(500).json({ error: '聊天请求失败' });
    }
});

app.post('/api/chat/stream', async (req, res) => {
    try {
        const { model, message } = req.body;
        if (!model || !message) {
            return res.status(400).json({ error: '模型名称和消息内容不能为空' });
        }
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, {
            model: model,
            prompt: message,
            stream: true
        }, {
            responseType: 'stream',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        response.data.pipe(res);
    } catch (error) {
        res.status(500).json({ error: '流式聊天请求失败' });
    }
});

// 代码生成API
app.post('/api/codegen', async (req, res) => {
    try {
        const { prompt, lang, model } = req.body;
        if (!prompt || !lang || !model) return res.status(400).json({ error: '描述、语言和模型不能为空' });
        const userPrompt = `请用${lang === 'cpp' ? 'C++' : lang === 'c' ? 'C' : lang === 'csharp' ? 'C#' : 'Python'}语言写一段代码，要求：${prompt}`;
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, {
            model,
            prompt: userPrompt,
            stream: false
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        let code = response.data.response || '';
        const match = code.match(/```[\s\S]*?\n([\s\S]*?)```/);
        if (match) code = match[1];
        res.json({ code });
    } catch (e) {
        res.status(500).json({ error: '代码生成失败' });
    }
});
// 办公助手API
app.post('/api/office', async (req, res) => {
    try {
        const { prompt, model } = req.body;
        if (!prompt || !model) return res.status(400).json({ error: '描述和模型不能为空' });
        const userPrompt = `你是一个专业的办公文员，请帮我完成如下任务：${prompt}`;
        const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, {
            model,
            prompt: userPrompt,
            stream: false
        }, {
            headers: { 'Content-Type': 'application/json' }
        });
        res.json({ content: response.data.response || '' });
    } catch (e) {
        res.status(500).json({ error: '内容生成失败' });
    }
});

// Word导出
app.post('/api/export/word', async (req, res) => {
    try {
        const { content } = req.body;
        if (!content) return res.status(400).json({ error: '内容不能为空' });
        const doc = new Document({
            sections: [{ properties: {}, children: [new Paragraph(content)] }]
        });
        const buffer = await Packer.toBuffer(doc);
        const filename = `office_${Date.now()}.docx`;
        const filepath = path.join(__dirname, 'public', filename);
        fs.writeFileSync(filepath, buffer);
        res.json({ url: `/${filename}` });
    } catch (e) {
        res.status(500).json({ error: 'Word导出失败' });
    }
});
// Excel导出
app.post('/api/export/excel', async (req, res) => {
    try {
        const { content } = req.body;
        if (!content) return res.status(400).json({ error: '内容不能为空' });
        const workbook = new ExcelJS.Workbook();
        const sheet = workbook.addWorksheet('Sheet1');
        // 简单按换行分割为行，按制表符或逗号分割为列
        content.split('\n').forEach(row => {
            sheet.addRow(row.split(/\t|,|\s{2,}/));
        });
        const filename = `office_${Date.now()}.xlsx`;
        const filepath = path.join(__dirname, 'public', filename);
        await workbook.xlsx.writeFile(filepath);
        res.json({ url: `/${filename}` });
    } catch (e) {
        res.status(500).json({ error: 'Excel导出失败' });
    }
});
// PDF导出
app.post('/api/export/pdf', async (req, res) => {
    try {
        const { content } = req.body;
        if (!content) return res.status(400).json({ error: '内容不能为空' });
        const filename = `office_${Date.now()}.pdf`;
        const filepath = path.join(__dirname, 'public', filename);
        const doc = new PDFDocument();
        doc.pipe(fs.createWriteStream(filepath));
        doc.fontSize(12).text(content);
        doc.end();
        doc.on('finish', () => {
            res.json({ url: `/${filename}` });
        });
    } catch (e) {
        res.status(500).json({ error: 'PDF导出失败' });
    }
});

app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, '0.0.0.0', () => {
    console.log(`服务器运行在 http://0.0.0.0:${PORT}`);
    console.log(`局域网访问地址: http://你的IP地址:${PORT}`);
    console.log(`Ollama API地址: ${OLLAMA_BASE_URL}`);
}); 